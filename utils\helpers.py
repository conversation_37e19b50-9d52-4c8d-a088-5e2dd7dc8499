from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from typing import List, <PERSON><PERSON>


def create_inline_keyboard(buttons: List[Tuple[str, str]], row_width: int = 2) -> InlineKeyboardMarkup:
    """Create inline keyboard from button list."""
    keyboard = []
    row = []
    
    for text, callback_data in buttons:
        row.append(InlineKeyboardButton(text=text, callback_data=callback_data))
        
        if len(row) >= row_width:
            keyboard.append(row)
            row = []
    
    if row:
        keyboard.append(row)
    
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def escape_html(text: str) -> str:
    """Escape HTML special characters."""
    return text.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")
