from aiogram import Router
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from keyboards.inline import (
    taxi_order_keyboard, 
    confirmation_keyboard,
    back_keyboard
)
from keyboards.reply import (
    location_request_keyboard,
    phone_request_keyboard,
    remove_keyboard
)
from keyboards.builders import InlineKeyboardBuilder, ReplyKeyboardBuilder

router = Router()


@router.callback_query(lambda c: c.data == "order_taxi")
async def order_taxi_menu(callback: CallbackQuery):
    """Show taxi order menu."""
    await callback.message.edit_text(
        "🚗 Choose taxi type:\n\n"
        "Select the type of taxi you'd like to order:",
        reply_markup=taxi_order_keyboard()
    )
    await callback.answer()


@router.callback_query(lambda c: c.data.startswith("taxi_"))
async def taxi_type_selected(callback: CallbackQuery):
    """Handle taxi type selection."""
    taxi_type = callback.data.split("_")[1]
    
    # Create confirmation keyboard with specific data
    keyboard = confirmation_keyboard(
        confirm_data=f"confirm_order_{taxi_type}",
        cancel_data="order_taxi"
    )
    
    await callback.message.edit_text(
        f"🚗 You selected: {taxi_type.title()}\n\n"
        "Do you want to proceed with this order?",
        reply_markup=keyboard
    )
    await callback.answer()


@router.callback_query(lambda c: c.data.startswith("confirm_order_"))
async def confirm_order(callback: CallbackQuery):
    """Confirm taxi order."""
    taxi_type = callback.data.split("_")[-1]
    
    await callback.message.edit_text(
        f"✅ Great! Your {taxi_type.title()} taxi has been ordered.\n\n"
        "Please share your location to complete the order:",
        reply_markup=back_keyboard("main_menu")
    )
    
    # Show location request keyboard
    await callback.message.answer(
        "📍 Please share your location:",
        reply_markup=location_request_keyboard()
    )
    await callback.answer()


@router.callback_query(lambda c: c.data == "support")
async def show_support(callback: CallbackQuery):
    """Show support information."""
    # Using keyboard builder for dynamic keyboard
    builder = InlineKeyboardBuilder()
    builder.button("📞 Call Support", "call_support")
    builder.button("💬 Chat Support", "chat_support")
    builder.row()
    builder.button("📧 Email Support", "email_support")
    builder.back_button("main_menu")
    
    await callback.message.edit_text(
        "📞 Support\n\n"
        "How can we help you?",
        reply_markup=builder.build()
    )
    await callback.answer()


@router.message(lambda message: message.location)
async def handle_location(message: Message):
    """Handle location sharing."""
    latitude = message.location.latitude
    longitude = message.location.longitude
    
    await message.answer(
        f"📍 Location received!\n"
        f"Latitude: {latitude}\n"
        f"Longitude: {longitude}\n\n"
        "Your taxi will arrive shortly!",
        reply_markup=remove_keyboard()
    )


@router.message(lambda message: message.contact)
async def handle_contact(message: Message):
    """Handle contact sharing."""
    phone = message.contact.phone_number
    
    # Save phone to database
    from utils.database import db
    await db.add_user(
        user_id=message.from_user.id,
        username=message.from_user.username,
        first_name=message.from_user.first_name,
        last_name=message.from_user.last_name,
        phone=phone
    )
    
    await message.answer(
        f"📱 Phone number saved: {phone}\n"
        "Thank you for sharing your contact information!",
        reply_markup=remove_keyboard()
    )


@router.callback_query(lambda c: c.data == "change_phone")
async def change_phone(callback: CallbackQuery):
    """Request phone number change."""
    await callback.message.answer(
        "📱 Please share your new phone number:",
        reply_markup=phone_request_keyboard()
    )
    await callback.answer()


# Example of using reply keyboard builder
@router.callback_query(lambda c: c.data == "order_history")
async def show_order_history(callback: CallbackQuery):
    """Show order history with reply keyboard."""
    # Using reply keyboard builder
    builder = ReplyKeyboardBuilder()
    builder.add_row("📅 Today", "📅 Yesterday")
    builder.add_row("📅 This Week", "📅 This Month")
    builder.cancel_button()
    builder.one_time(True)
    
    await callback.message.answer(
        "📊 Order History\n\n"
        "Select time period:",
        reply_markup=builder.build()
    )
    await callback.answer()


@router.message(lambda message: message.text and message.text.startswith("📅"))
async def handle_history_period(message: Message):
    """Handle history period selection."""
    period = message.text.replace("📅 ", "")
    
    await message.answer(
        f"📊 Showing orders for: {period}\n\n"
        "You have no orders for this period.",
        reply_markup=remove_keyboard()
    )
