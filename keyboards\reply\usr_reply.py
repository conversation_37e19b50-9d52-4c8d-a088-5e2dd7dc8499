from aiogram.types import Reply<PERSON><PERSON>boardMarkup, KeyboardButton, Reply<PERSON><PERSON>board<PERSON>emove


def main_user_keyboard() -> ReplyKeyboardMarkup:
    """Create main user reply keyboard with all options."""
    keyboard = [
        [KeyboardButton(text="🚗 Taxi"), KeyboardButton(text="📦 Delivery")],
        [Keyboard<PERSON>utton(text="🚙 Join Drivers"), KeyboardButton(text="👤 Profile")],
        [KeyboardButton(text="📞 Contact")]
    ]
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True,
        persistent=True
    )


def cancel_keyboard() -> ReplyKeyboardMarkup:
    """Create cancel keyboard for ordering process."""
    keyboard = [
        [KeyboardButton(text="❌ Cancel")]
    ]
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True,
        one_time_keyboard=False
    )


def phone_request_keyboard() -> ReplyKeyboardMarkup:
    """Create phone request keyboard."""
    keyboard = [
        [KeyboardButton(text="📱 Share Phone", request_contact=True)],
        [KeyboardButton(text="🔙 Back"), KeyboardButton(text="❌ Cancel")]
    ]
    return Reply<PERSON><PERSON>boardMarkup(
        keyboard=keyboard,
        resize_keyboard=True,
        one_time_keyboard=True
    )


def location_request_keyboard() -> ReplyKeyboardMarkup:
    """Create location request keyboard."""
    keyboard = [
        [KeyboardButton(text="📍 Share Location", request_location=True)],
        [KeyboardButton(text="❌ Cancel")]
    ]
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True,
        one_time_keyboard=True
    )


def remove_keyboard() -> ReplyKeyboardRemove:
    """Remove reply keyboard."""
    return ReplyKeyboardRemove()


def taxi_types_keyboard() -> ReplyKeyboardMarkup:
    """Create taxi types keyboard."""
    keyboard = [
        [KeyboardButton(text="🚗 Economy"), KeyboardButton(text="🚙 Comfort")],
        [KeyboardButton(text="🚐 Business"), KeyboardButton(text="🏎️ Premium")],
        [KeyboardButton(text="❌ Cancel")]
    ]
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True,
        one_time_keyboard=True
    )
