from aiogram.fsm.state import State, StatesGroup


class UserStates(StatesGroup):
    """User interaction states."""
    waiting_for_input = State()
    processing = State()
    confirmation = State()


class RegistrationStates(StatesGroup):
    """User registration states."""
    waiting_for_name = State()
    waiting_for_phone = State()
    waiting_for_email = State()


class TaxiOrderStates(StatesGroup):
    """Taxi ordering states."""
    choosing_direction = State()
    choosing_people_count = State()
    waiting_for_phone = State()
    confirmation = State()
    additional_info = State()
    waiting_pickup_location = State()
    waiting_destination_location = State()
    waiting_description = State()


class DeliveryOrderStates(StatesGroup):
    """Delivery ordering states."""
    choosing_direction = State()
    waiting_for_phone = State()
    waiting_pickup_location = State()
    waiting_destination_location = State()
    waiting_description = State()
    confirmation = State()


class DriverRegistrationStates(StatesGroup):
    """Driver registration states."""
    waiting_for_phone = State()
    waiting_for_car_info = State()
    waiting_for_license = State()
    confirmation = State()
