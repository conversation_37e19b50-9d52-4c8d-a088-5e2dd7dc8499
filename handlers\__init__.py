from aiogram import Dispatcher
from .user import start_router, taxi_router
from .admin import admin_panel_router, user_management_router, statistics_router


def setup_routers(dp: Dispatcher) -> None:
    """Setup all routers."""
    # User routers
    dp.include_router(start_router)
    dp.include_router(taxi_router)
    
    # Admin routers
    dp.include_router(admin_panel_router)
    dp.include_router(user_management_router)
    dp.include_router(statistics_router)


__all__ = ["setup_routers"]
