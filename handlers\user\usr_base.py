from aiogram import Router, F
from aiogram.filters import CommandStart
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from utils.database import db
from keyboards.reply.usr_reply import main_user_keyboard, remove_keyboard, settings_keyboard
from keyboards.inline.usr_inline import driver_registration_keyboard
from filters.custom_filters import IsUserFilter

router = Router()
router.message.filter(IsUserFilter())
router.callback_query.filter(IsUserFilter())


@router.message(CommandStart())
async def cmd_start(message: Message, state: FSMContext):
    """Handle /start command for users."""
    await state.clear()

    # Save user to database
    user = message.from_user
    await db.add_user(
        user_id=user.id,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name,
        user_type='user'
    )

    await message.answer(
        f"Hello, {message.from_user.full_name}! 👋\n\n"
        "🚗 Welcome to TaxiBot!\n"
        "Your reliable taxi and delivery service between Toshkent and Beshariq.\n\n"
        "Choose an option from the menu below:",
        reply_markup=main_user_keyboard()
    )


@router.message(F.text == "🚗 Taxi")
async def taxi_option(message: Message, state: FSMContext):
    """Handle taxi option selection."""
    from handlers.user.taxi import start_taxi_order
    await start_taxi_order(message, state)


@router.message(F.text == "📦 Delivery")
async def delivery_option(message: Message, state: FSMContext):
    """Handle delivery option selection."""
    from handlers.user.delivery import start_delivery_order
    await start_delivery_order(message, state)


@router.message(F.text == "🚙 Join Drivers")
async def join_drivers_option(message: Message, state: FSMContext):
    """Handle join drivers option."""
    await message.answer(
        "🚙 <b>Driver Registration</b>\n\n"
        "Join our team of professional drivers and start earning!\n\n"
        "📋 <b>Requirements:</b>\n"
        "• Valid driver's license\n"
        "• Own vehicle in good condition\n"
        "• Experience driving between cities\n"
        "• Clean driving record\n\n"
        "Would you like to proceed with registration?",
        reply_markup=driver_registration_keyboard()
    )


@router.message(F.text == "👤 Profile")
async def profile_option(message: Message, state: FSMContext):
    """Handle profile option."""
    user_data = await db.get_user(message.from_user.id)

    if user_data:
        profile_text = (
            f"👤 <b>Your Profile</b>\n\n"
            f"🆔 ID: {user_data['user_id']}\n"
            f"👤 Name: {user_data['first_name']} {user_data['last_name'] or ''}\n"
            f"📱 Phone: {user_data['phone'] or 'Not provided'}\n"
            f"📅 Joined: {user_data['created_at'].strftime('%d.%m.%Y')}\n"
            f"🎭 Type: {user_data['user_type'].title()}"
        )
    else:
        profile_text = "❌ Profile not found. Please restart the bot with /start"

    await message.answer(profile_text, reply_markup=remove_keyboard())


@router.message(F.text == "📞 Contact")
async def contact_option(message: Message, state: FSMContext):
    """Handle contact option."""
    contact_text = (
        "📞 <b>Contact Information</b>\n\n"
        "🏢 <b>TaxiBot Support</b>\n\n"
        "📱 Phone: +998 XX XXX XX XX\n"
        "💬 Telegram: @taxibot_support\n"
        "📧 Email: <EMAIL>\n\n"
        "🕐 <b>Working Hours:</b>\n"
        "24/7 - We're always here to help!\n\n"
        "For urgent issues, please call our hotline."
    )

    await message.answer(contact_text, reply_markup=remove_keyboard())


@router.message(F.text == "❌ Cancel")
async def cancel_operation(message: Message, state: FSMContext):
    """Handle cancel operation."""
    await state.clear()
    await message.answer(
        "❌ Operation cancelled.\n\n"
        "Choose an option from the menu:",
        reply_markup=main_user_keyboard()
    )


@router.callback_query(lambda c: c.data == "settings")
async def show_settings(callback: CallbackQuery):
    """Show settings menu."""
    await callback.message.edit_text(
        "⚙️ Settings\n\n"
        "Configure your preferences:",
        reply_markup=settings_keyboard()
    )
    await callback.answer()
