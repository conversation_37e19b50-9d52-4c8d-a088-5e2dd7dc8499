from aiogram.types import (
    InlineKeyboardMarkup, 
    InlineKeyboardButton,
    ReplyKeyboardMarkup,
    KeyboardButton
)
from typing import List, Tuple, Optional, Union


class InlineKeyboardBuilder:
    """Builder for inline keyboards."""
    
    def __init__(self):
        self.buttons: List[List[InlineKeyboardButton]] = []
        self.current_row: List[InlineKeyboardButton] = []
    
    def button(self, text: str, callback_data: str = None, url: str = None) -> 'InlineKeyboardBuilder':
        """Add button to current row."""
        if callback_data:
            button = InlineKeyboardButton(text=text, callback_data=callback_data)
        elif url:
            button = InlineKeyboardButton(text=text, url=url)
        else:
            raise ValueError("Either callback_data or url must be provided")
        
        self.current_row.append(button)
        return self
    
    def row(self) -> 'InlineKeyboardBuilder':
        """Start new row."""
        if self.current_row:
            self.buttons.append(self.current_row)
            self.current_row = []
        return self
    
    def add_row(self, *buttons_data: Tuple[str, str]) -> 'InlineKeyboardBuilder':
        """Add complete row with button data."""
        self.row()
        for text, callback_data in buttons_data:
            self.button(text, callback_data)
        return self
    
    def back_button(self, callback_data: str = "back") -> 'InlineKeyboardBuilder':
        """Add back button."""
        self.row()
        self.button("🔙 Back", callback_data)
        return self
    
    def pagination(
        self, 
        current_page: int, 
        total_pages: int, 
        data_prefix: str = "page"
    ) -> 'InlineKeyboardBuilder':
        """Add pagination buttons."""
        self.row()
        
        if current_page > 1:
            self.button("◀️", f"{data_prefix}_{current_page - 1}")
        
        self.button(f"{current_page}/{total_pages}", "current_page")
        
        if current_page < total_pages:
            self.button("▶️", f"{data_prefix}_{current_page + 1}")
        
        return self
    
    def build(self) -> InlineKeyboardMarkup:
        """Build the keyboard."""
        if self.current_row:
            self.buttons.append(self.current_row)
        
        return InlineKeyboardMarkup(inline_keyboard=self.buttons)


class ReplyKeyboardBuilder:
    """Builder for reply keyboards."""
    
    def __init__(self):
        self.buttons: List[List[KeyboardButton]] = []
        self.current_row: List[KeyboardButton] = []
        self.resize_keyboard = True
        self.one_time_keyboard = False
        self.persistent = False
    
    def button(
        self, 
        text: str, 
        request_contact: bool = False,
        request_location: bool = False
    ) -> 'ReplyKeyboardBuilder':
        """Add button to current row."""
        button = KeyboardButton(
            text=text,
            request_contact=request_contact,
            request_location=request_location
        )
        self.current_row.append(button)
        return self
    
    def row(self) -> 'ReplyKeyboardBuilder':
        """Start new row."""
        if self.current_row:
            self.buttons.append(self.current_row)
            self.current_row = []
        return self
    
    def add_row(self, *button_texts: str) -> 'ReplyKeyboardBuilder':
        """Add complete row with button texts."""
        self.row()
        for text in button_texts:
            self.button(text)
        return self
    
    def contact_button(self, text: str = "📱 Share Phone") -> 'ReplyKeyboardBuilder':
        """Add contact request button."""
        self.button(text, request_contact=True)
        return self
    
    def location_button(self, text: str = "📍 Share Location") -> 'ReplyKeyboardBuilder':
        """Add location request button."""
        self.button(text, request_location=True)
        return self
    
    def cancel_button(self, text: str = "❌ Cancel") -> 'ReplyKeyboardBuilder':
        """Add cancel button."""
        self.row()
        self.button(text)
        return self
    
    def resize(self, resize: bool = True) -> 'ReplyKeyboardBuilder':
        """Set resize keyboard option."""
        self.resize_keyboard = resize
        return self
    
    def one_time(self, one_time: bool = True) -> 'ReplyKeyboardBuilder':
        """Set one time keyboard option."""
        self.one_time_keyboard = one_time
        return self
    
    def make_persistent(self, persistent: bool = True) -> 'ReplyKeyboardBuilder':
        """Set persistent keyboard option."""
        self.persistent = persistent
        return self
    
    def build(self) -> ReplyKeyboardMarkup:
        """Build the keyboard."""
        if self.current_row:
            self.buttons.append(self.current_row)
        
        return ReplyKeyboardMarkup(
            keyboard=self.buttons,
            resize_keyboard=self.resize_keyboard,
            one_time_keyboard=self.one_time_keyboard,
            persistent=self.persistent
        )


# Convenience functions for quick keyboard creation
def quick_inline(*buttons_data: Tuple[str, str], row_width: int = 2) -> InlineKeyboardMarkup:
    """Quickly create inline keyboard."""
    builder = InlineKeyboardBuilder()
    
    for i, (text, callback_data) in enumerate(buttons_data):
        builder.button(text, callback_data)
        if (i + 1) % row_width == 0:
            builder.row()
    
    return builder.build()


def quick_reply(*button_texts: str, row_width: int = 2) -> ReplyKeyboardMarkup:
    """Quickly create reply keyboard."""
    builder = ReplyKeyboardBuilder()
    
    for i, text in enumerate(button_texts):
        builder.button(text)
        if (i + 1) % row_width == 0:
            builder.row()
    
    return builder.build()
