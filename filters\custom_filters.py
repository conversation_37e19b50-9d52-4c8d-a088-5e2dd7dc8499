from aiogram.filters import <PERSON>Filter
from aiogram.types import Message
from config.settings import get_settings


class IsAdminFilter(BaseFilter):
    """Filter for admin users."""
    
    async def __call__(self, message: Message) -> bool:
        settings = get_settings()
        return message.from_user.id in settings.ADMIN_IDS if settings.ADMIN_IDS else False


class TextLengthFilter(BaseFilter):
    """Filter messages by text length."""
    
    def __init__(self, min_length: int = 0, max_length: int = 4096):
        self.min_length = min_length
        self.max_length = max_length

    async def __call__(self, message: Message) -> bool:
        if not message.text:
            return False
        return self.min_length <= len(message.text) <= self.max_length
