from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from keyboards.inline.usr_inline import delivery_direction_keyboard
from keyboards.reply.usr_reply import (
    cancel_keyboard,
    phone_request_keyboard,
    main_user_keyboard,
    remove_keyboard
)
from states.usr_states import DeliveryOrderStates
from loader import db
from filters.custom_filters import IsUserFilter

router = Router()
router.message.filter(IsUserFilter())
router.callback_query.filter(IsUserFilter())


async def start_delivery_order(message: Message, state: FSMContext):
    """Start delivery ordering process."""
    await state.set_state(DeliveryOrderStates.choosing_direction)

    # Show cancel keyboard
    await message.answer(
        "You can cancel anytime by pressing the Cancel button below:",
        reply_markup=cancel_keyboard()
    )

    await message.answer(
        "📦 <b>Delivery Order</b>\n\n"
        "Please select your delivery direction:",
        reply_markup=delivery_direction_keyboard()
    )

    


@router.callback_query(F.data.startswith("delivery_direction_"), DeliveryOrderStates.choosing_direction)
async def delivery_direction_selected(callback: CallbackQuery, state: FSMContext):
    """Handle delivery direction selection."""
    direction_data = callback.data.replace("delivery_direction_", "")

    # Store direction in state
    await state.update_data(direction=direction_data)
    await state.set_state(DeliveryOrderStates.waiting_for_phone)

    # Format direction for display
    if direction_data == "toshkent_beshariq":
        direction_text = "Toshkent → Beshariq"
    else:
        direction_text = "Beshariq → Toshkent"

    await callback.message.edit_text(
        f"📦 <b>Delivery Order</b>\n\n"
        f"📍 Direction: {direction_text}\n\n"
        "📱 Please share your phone number or type it manually:"
    )

    # Send phone request keyboard as new message
    await callback.message.answer(
        "You can share your contact or type your phone number:",
        reply_markup=phone_request_keyboard()
    )
    await callback.answer()


@router.message(DeliveryOrderStates.waiting_for_phone)
async def delivery_phone_received(message: Message, state: FSMContext):
    """Handle phone number input for delivery."""
    phone = None

    if message.contact:
        phone = message.contact.phone_number
    elif message.text and message.text != "❌ Cancel":
        # Validate phone number format (basic validation)
        phone_text = message.text.strip()
        if phone_text.startswith('+') or phone_text.isdigit():
            phone = phone_text
        else:
            await message.answer(
                "❌ Invalid phone number format.\n"
                "Please enter a valid phone number or share your contact:",
                reply_markup=phone_request_keyboard()
            )
            return
    else:
        return

    # Store phone and move to confirmation
    await state.update_data(phone=phone)
    await state.set_state(DeliveryOrderStates.confirmation)

    # Show confirmation directly
    await show_delivery_confirmation(message, state)


# Optional information handlers (if user wants to add details)
@router.message(DeliveryOrderStates.waiting_pickup_location)
async def delivery_pickup_received(message: Message, state: FSMContext):
    """Handle pickup location input for delivery."""
    if message.text and message.text != "❌ Cancel":
        pickup_location = message.text.strip()
        await state.update_data(pickup_location=pickup_location)
        await state.set_state(DeliveryOrderStates.confirmation)

        await message.answer(
            f"✅ Pickup location added: {pickup_location}\n\n"
            "Returning to confirmation..."
        )

        await show_delivery_confirmation(message, state)


@router.message(DeliveryOrderStates.waiting_destination_location)
async def delivery_destination_received(message: Message, state: FSMContext):
    """Handle destination location input for delivery."""
    if message.text and message.text != "❌ Cancel":
        destination_location = message.text.strip()
        await state.update_data(destination_location=destination_location)
        await state.set_state(DeliveryOrderStates.confirmation)

        await message.answer(
            f"✅ Destination location added: {destination_location}\n\n"
            "Returning to confirmation..."
        )

        await show_delivery_confirmation(message, state)


@router.message(DeliveryOrderStates.waiting_description)
async def delivery_description_received(message: Message, state: FSMContext):
    """Handle description input for delivery."""
    if message.text and message.text != "❌ Cancel":
        description = message.text.strip()
        await state.update_data(description=description)
        await state.set_state(DeliveryOrderStates.confirmation)

        await message.answer(
            f"✅ Description added: {description}\n\n"
            "Returning to confirmation..."
        )

        await show_delivery_confirmation(message, state)


async def show_delivery_confirmation(message: Message, state: FSMContext):
    """Show delivery order confirmation."""
    data = await state.get_data()
    direction = data.get('direction', '')
    phone = data.get('phone', '')
    pickup_location = data.get('pickup_location', '')
    destination_location = data.get('destination_location', '')
    description = data.get('description', '')

    # Format direction
    if direction == "toshkent_beshariq":
        direction_text = "Toshkent → Beshariq"
    else:
        direction_text = "Beshariq → Toshkent"

    # Build confirmation text
    confirmation_text = (
        f"📦 <b>Delivery Order Confirmation</b>\n\n"
        f"📍 <b>Direction:</b> {direction_text}\n"
        f"📱 <b>Phone:</b> {phone}\n"
    )

    # Add optional information if provided
    if pickup_location:
        confirmation_text += f"📍 <b>Pickup:</b> {pickup_location}\n"
    if destination_location:
        confirmation_text += f"🎯 <b>Destination:</b> {destination_location}\n"
    if description:
        confirmation_text += f"📝 <b>Description:</b> {description}\n"

    confirmation_text += "\n✅ Please review your delivery order:"

    # Create confirmation keyboard with optional info buttons
    from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

    buttons = []

    # Add optional info buttons if not already provided
    if not pickup_location:
        buttons.append([InlineKeyboardButton(text="📍 Add Pickup Location", callback_data="add_delivery_pickup")])
    if not destination_location:
        buttons.append([InlineKeyboardButton(text="🎯 Add Destination", callback_data="add_delivery_destination")])
    if not description:
        buttons.append([InlineKeyboardButton(text="📝 Add Description", callback_data="add_delivery_description")])

    # Add main action buttons
    buttons.append([InlineKeyboardButton(text="✅ Send Order", callback_data="send_delivery_order")])
    buttons.append([InlineKeyboardButton(text="❌ Cancel", callback_data="cancel_delivery")])

    keyboard = InlineKeyboardMarkup(inline_keyboard=buttons)

    await message.answer(confirmation_text, reply_markup=keyboard)


@router.callback_query(F.data == "send_delivery_order", DeliveryOrderStates.confirmation)
async def send_delivery_order(callback: CallbackQuery, state: FSMContext):
    """Send the delivery order."""
    data = await state.get_data()
    user_id = callback.from_user.id

    # Create order in database
    order_id = await db.create_delivery_order(
        user_id=user_id,
        direction=data.get('direction', ''),
        phone=data.get('phone', ''),
        pickup_location=data.get('pickup_location'),
        destination_location=data.get('destination_location'),
        description=data.get('description')
    )

    if order_id:
        await callback.message.edit_text(
            f"✅ <b>Delivery Order Sent!</b>\n\n"
            f"🆔 Order ID: #{order_id}\n\n"
            f"Your delivery order has been sent to our drivers.\n"
            f"You will be contacted shortly at {data.get('phone')}.\n\n"
            f"Thank you for using TaxiBot! 📦"
        )

        # Clear state and return to main menu
        await state.clear()
        await callback.message.answer(
            "Choose another option:",
            reply_markup=main_user_keyboard()
        )
    else:
        await callback.message.edit_text(
            "❌ <b>Error</b>\n\n"
            "Failed to send your delivery order. Please try again."
        )

    await callback.answer()


# Optional information handlers
@router.callback_query(F.data == "add_delivery_pickup", DeliveryOrderStates.confirmation)
async def add_delivery_pickup(callback: CallbackQuery, state: FSMContext):
    """Add pickup location for delivery."""
    await state.set_state(DeliveryOrderStates.waiting_pickup_location)
    await callback.message.edit_text(
        "📍 <b>Pickup Location</b>\n\n"
        "Please type the pickup address where we should collect the item:"
    )
    await callback.answer()


@router.callback_query(F.data == "add_delivery_destination", DeliveryOrderStates.confirmation)
async def add_delivery_destination(callback: CallbackQuery, state: FSMContext):
    """Add destination location for delivery."""
    await state.set_state(DeliveryOrderStates.waiting_destination_location)
    await callback.message.edit_text(
        "🎯 <b>Destination Location</b>\n\n"
        "Please type the destination address where we should deliver the item:"
    )
    await callback.answer()


@router.callback_query(F.data == "add_delivery_description", DeliveryOrderStates.confirmation)
async def add_delivery_description(callback: CallbackQuery, state: FSMContext):
    """Add description for delivery."""
    await state.set_state(DeliveryOrderStates.waiting_description)
    await callback.message.edit_text(
        "📝 <b>Item Description</b>\n\n"
        "Please describe what you want to deliver (size, weight, special handling, etc.):"
    )
    await callback.answer()


@router.callback_query(F.data == "cancel_delivery", DeliveryOrderStates.confirmation)
async def cancel_delivery_order(callback: CallbackQuery, state: FSMContext):
    """Cancel delivery order."""
    await state.clear()
    await callback.message.edit_text(
        "❌ Delivery order cancelled.\n\n"
        "Choose another option:"
    )

    await callback.message.answer(
        "Main menu:",
        reply_markup=main_user_keyboard()
    )
    await callback.answer()