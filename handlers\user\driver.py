from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from keyboards.reply.usr_reply import phone_request_keyboard, main_user_keyboard, remove_keyboard
from states.usr_states import DriverRegistrationStates
from utils.database import db
from filters.custom_filters import IsUserFilter

router = Router()
router.message.filter(IsUserFilter())
router.callback_query.filter(IsUserFilter())


@router.callback_query(F.data == "register_driver")
async def start_driver_registration(callback: CallbackQuery, state: FSMContext):
    """Start driver registration process."""
    await state.set_state(DriverRegistrationStates.waiting_for_phone)

    await callback.message.edit_text(
        "🚙 <b>Driver Registration</b>\n\n"
        "To register as a driver, we need some information from you.\n\n"
        "📱 First, please share your phone number:"
    )

    await callback.message.answer(
        "You can share your contact or type your phone number:",
        reply_markup=phone_request_keyboard()
    )
    await callback.answer()


@router.callback_query(F.data == "driver_requirements")
async def show_driver_requirements(callback: CallbackQuery):
    """Show driver requirements."""
    requirements_text = (
        "🚙 <b>Driver Requirements</b>\n\n"
        "📋 <b>To become a driver, you need:</b>\n\n"
        "✅ Valid driver's license (Category B or higher)\n"
        "✅ Own vehicle in good condition\n"
        "✅ Vehicle registration documents\n"
        "✅ Insurance policy\n"
        "✅ Clean driving record (no major violations)\n"
        "✅ Experience driving between cities\n"
        "✅ Age 21 or older\n"
        "✅ Good knowledge of routes\n\n"
        "💰 <b>Benefits:</b>\n"
        "• Flexible working hours\n"
        "• Competitive rates\n"
        "• Weekly payments\n"
        "• 24/7 support\n\n"
        "📞 For more information, contact our support team."
    )

    await callback.message.edit_text(requirements_text)
    await callback.answer()


@router.message(DriverRegistrationStates.waiting_for_phone)
async def driver_phone_received(message: Message, state: FSMContext):
    """Handle phone number for driver registration."""
    phone = None

    if message.contact:
        phone = message.contact.phone_number
    elif message.text and message.text != "❌ Cancel":
        phone_text = message.text.strip()
        if phone_text.startswith('+') or phone_text.isdigit():
            phone = phone_text
        else:
            await message.answer(
                "❌ Invalid phone number format.\n"
                "Please enter a valid phone number or share your contact:",
                reply_markup=phone_request_keyboard()
            )
            return
    else:
        return

    await state.update_data(phone=phone)
    await state.set_state(DriverRegistrationStates.waiting_for_car_info)

    await message.answer(
        "🚗 <b>Vehicle Information</b>\n\n"
        "Please provide your vehicle information:\n"
        "• Make and model\n"
        "• Year\n"
        "• Color\n"
        "• License plate number\n\n"
        "Example: Toyota Camry 2020, White, 01A123BC",
        reply_markup=remove_keyboard()
    )


@router.message(DriverRegistrationStates.waiting_for_car_info)
async def car_info_received(message: Message, state: FSMContext):
    """Handle car information."""
    if message.text and message.text != "❌ Cancel":
        car_info = message.text.strip()
        await state.update_data(car_info=car_info)
        await state.set_state(DriverRegistrationStates.waiting_for_license)

        await message.answer(
            "📄 <b>Driver's License</b>\n\n"
            "Please provide your driver's license information:\n"
            "• License number\n"
            "• Issue date\n"
            "• Expiry date\n"
            "• Category\n\n"
            "Example: *********, 15.01.2020, 15.01.2030, Category B"
        )


@router.message(DriverRegistrationStates.waiting_for_license)
async def license_info_received(message: Message, state: FSMContext):
    """Handle license information."""
    if message.text and message.text != "❌ Cancel":
        license_info = message.text.strip()
        await state.update_data(license_info=license_info)
        await state.set_state(DriverRegistrationStates.confirmation)

        # Show confirmation
        await show_driver_confirmation(message, state)


async def show_driver_confirmation(message: Message, state: FSMContext):
    """Show driver registration confirmation."""
    data = await state.get_data()
    phone = data.get('phone', '')
    car_info = data.get('car_info', '')
    license_info = data.get('license_info', '')

    confirmation_text = (
        f"🚙 <b>Driver Registration Confirmation</b>\n\n"
        f"📱 <b>Phone:</b> {phone}\n"
        f"🚗 <b>Vehicle:</b> {car_info}\n"
        f"📄 <b>License:</b> {license_info}\n\n"
        f"✅ Please review your information and submit:"
    )

    from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="✅ Submit Application", callback_data="submit_driver_application")],
        [InlineKeyboardButton(text="❌ Cancel", callback_data="cancel_driver_registration")]
    ])

    await message.answer(confirmation_text, reply_markup=keyboard)


@router.callback_query(F.data == "submit_driver_application")
async def submit_driver_application(callback: CallbackQuery, state: FSMContext):
    """Submit driver application."""
    data = await state.get_data()
    user_id = callback.from_user.id

    # Update user type to driver (pending approval)
    success = await db.update_user_type(
        user_id=user_id,
        user_type='driver',
        driver_status='pending',
        car_info=data.get('car_info', ''),
        license_info=data.get('license_info', '')
    )

    if success:
        await callback.message.edit_text(
            f"✅ <b>Application Submitted!</b>\n\n"
            f"Your driver application has been submitted for review.\n"
            f"Our team will contact you at {data.get('phone')} within 24-48 hours.\n\n"
            f"📋 <b>Next Steps:</b>\n"
            f"• Document verification\n"
            f"• Vehicle inspection\n"
            f"• Background check\n"
            f"• Training session\n\n"
            f"Thank you for your interest in joining our team! 🚙"
        )

        await state.clear()
        await callback.message.answer(
            "You can continue using the bot as a regular user:",
            reply_markup=main_user_keyboard()
        )
    else:
        await callback.message.edit_text(
            "❌ <b>Error</b>\n\n"
            "Failed to submit your application. Please try again."
        )

    await callback.answer()


@router.callback_query(F.data == "cancel_driver_registration")
async def cancel_driver_registration(callback: CallbackQuery, state: FSMContext):
    """Cancel driver registration."""
    await state.clear()
    await callback.message.edit_text(
        "❌ Driver registration cancelled.\n\n"
        "You can try again anytime from the main menu."
    )

    await callback.message.answer(
        "Main menu:",
        reply_markup=main_user_keyboard()
    )
    await callback.answer()