from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton


def taxi_direction_keyboard() -> InlineKeyboardMarkup:
    """Create taxi direction selection keyboard."""
    buttons = [
        [InlineKeyboardButton(text="🚗 Toshkent → Beshariq", callback_data="direction_toshkent_beshariq")],
        [InlineKeyboardButton(text="🚗 Beshariq → Toshkent", callback_data="direction_beshariq_toshkent")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def taxi_people_count_keyboard() -> InlineKeyboardMarkup:
    """Create people count selection keyboard."""
    buttons = [
        [
            InlineKeyboardButton(text="1️⃣ 1 person", callback_data="people_1"),
            InlineKeyboardButton(text="2️⃣ 2 people", callback_data="people_2")
        ],
        [
            InlineKeyboardButton(text="3️⃣ 3 people", callback_data="people_3"),
            InlineKeyboardButton(text="4️⃣ 4 people", callback_data="people_4")
        ],
        [
            InlineKeyboardButton(text="🔙 Back", callback_data="back_to_direction")
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def taxi_confirmation_keyboard() -> InlineKeyboardMarkup:
    """Create taxi order confirmation keyboard."""
    buttons = [
        [InlineKeyboardButton(text="🔙 Back", callback_data="back_to_phone")],
        [InlineKeyboardButton(text="➕ Additional Info", callback_data="additional_info")],
        [InlineKeyboardButton(text="✅ Send Order", callback_data="send_taxi_order")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def additional_info_keyboard() -> InlineKeyboardMarkup:
    """Create additional info selection keyboard."""
    buttons = [
        [InlineKeyboardButton(text="📍 Add Pickup Location", callback_data="add_pickup_location")],
        [InlineKeyboardButton(text="🎯 Add Destination", callback_data="add_destination")],
        [InlineKeyboardButton(text="📝 Add Description", callback_data="add_description")],
        [InlineKeyboardButton(text="🔙 Back to Confirmation", callback_data="back_to_confirmation")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def delivery_direction_keyboard() -> InlineKeyboardMarkup:
    """Create delivery direction selection keyboard."""
    buttons = [
        [InlineKeyboardButton(text="📦 Toshkent → Beshariq", callback_data="delivery_direction_toshkent_beshariq")],
        [InlineKeyboardButton(text="📦 Beshariq → Toshkent", callback_data="delivery_direction_beshariq_toshkent")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def driver_registration_keyboard() -> InlineKeyboardMarkup:
    """Create driver registration keyboard."""
    buttons = [
        [InlineKeyboardButton(text="🚙 Register as Driver", callback_data="register_driver")],
        [InlineKeyboardButton(text="❓ Driver Requirements", callback_data="driver_requirements")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def order_status_keyboard(order_id: str) -> InlineKeyboardMarkup:
    """Create order status keyboard."""
    buttons = [
        [InlineKeyboardButton(text="📍 Track Order", callback_data=f"track_{order_id}")],
        [InlineKeyboardButton(text="📞 Call Driver", callback_data=f"call_{order_id}")],
        [InlineKeyboardButton(text="❌ Cancel Order", callback_data=f"cancel_{order_id}")],
        [InlineKeyboardButton(text="🔙 Back to Menu", callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def confirmation_keyboard(confirm_data: str = "confirm", cancel_data: str = "cancel") -> InlineKeyboardMarkup:
    """Create confirmation keyboard."""
    buttons = [
        [
            InlineKeyboardButton(text="✅ Yes", callback_data=confirm_data),
            InlineKeyboardButton(text="❌ No", callback_data=cancel_data)
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)
