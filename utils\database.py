import asyncpg
import logging
from typing import Optional, List, Dict, Any
from config.settings import get_settings

logger = logging.getLogger(__name__)


class Database:
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self.settings = get_settings()

    async def connect(self):
        """Create database connection pool."""
        try:
            self.pool = await asyncpg.create_pool(
                self.settings.DATABASE_URL,
                min_size=1,
                max_size=10,
                command_timeout=60
            )
            logger.info("Database connected successfully")
            await self.create_tables()
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise

    async def disconnect(self):
        """Close database connection pool."""
        if self.pool:
            await self.pool.close()
            logger.info("Database disconnected")

    async def create_tables(self):
        """Create necessary tables."""
        query = """
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            user_id BIGINT UNIQUE NOT NULL,
            username <PERSON><PERSON><PERSON><PERSON>(255),
            first_name <PERSON><PERSON><PERSON><PERSON>(255),
            last_name <PERSON><PERSON><PERSON><PERSON>(255),
            phone VARCHAR(20),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_users_user_id ON users(user_id);
        """
        
        async with self.pool.acquire() as conn:
            await conn.execute(query)
        logger.info("Tables created successfully")

    async def add_user(self, user_id: int, username: str = None, 
                      first_name: str = None, last_name: str = None, 
                      phone: str = None) -> bool:
        """Add or update user in database."""
        query = """
        INSERT INTO users (user_id, username, first_name, last_name, phone)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (user_id) 
        DO UPDATE SET 
            username = EXCLUDED.username,
            first_name = EXCLUDED.first_name,
            last_name = EXCLUDED.last_name,
            phone = COALESCE(EXCLUDED.phone, users.phone),
            updated_at = CURRENT_TIMESTAMP
        """
        
        try:
            async with self.pool.acquire() as conn:
                await conn.execute(query, user_id, username, first_name, last_name, phone)
            return True
        except Exception as e:
            logger.error(f"Error adding user {user_id}: {e}")
            return False

    async def get_user(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user from database."""
        query = "SELECT * FROM users WHERE user_id = $1"
        
        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(query, user_id)
                return dict(row) if row else None
        except Exception as e:
            logger.error(f"Error getting user {user_id}: {e}")
            return None

    async def execute_query(self, query: str, *args) -> List[Dict[str, Any]]:
        """Execute custom query."""
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(query, *args)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            return []
