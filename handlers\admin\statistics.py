from aiogram import Router
from aiogram.types import CallbackQuery
from filters.custom_filters import IsAdminFilter
from keyboards.inline.adm_inline import statistics_keyboard
from utils.database import db
import asyncio
from datetime import datetime, timedelta
from config.settings import get_settings

# Get admin IDs from settings
settings = get_settings()
ADMIN_IDS = settings.ADMIN_IDS

router = Router()
router.callback_query.filter(IsAdminFilter(ADMIN_IDS))


@router.callback_query(lambda c: c.data == "statistics")
async def show_statistics_menu(callback: CallbackQuery):
    """Show statistics menu."""
    await callback.message.edit_text(
        "📊 Statistics\n\n"
        "Choose statistics type:",
        reply_markup=statistics_keyboard()
    )
    await callback.answer()


@router.callback_query(lambda c: c.data == "user_stats")
async def show_user_statistics(callback: CallbackQuery):
    """Show user statistics."""
    try:
        # Get user statistics
        total_users = await db.execute_query("SELECT COUNT(*) as count FROM users")
        today_users = await db.execute_query(
            "SELECT COUNT(*) as count FROM users WHERE created_at >= $1",
            datetime.now().date()
        )
        week_users = await db.execute_query(
            "SELECT COUNT(*) as count FROM users WHERE created_at >= $1",
            datetime.now() - timedelta(days=7)
        )
        month_users = await db.execute_query(
            "SELECT COUNT(*) as count FROM users WHERE created_at >= $1",
            datetime.now() - timedelta(days=30)
        )
        
        total_count = total_users[0]['count'] if total_users else 0
        today_count = today_users[0]['count'] if today_users else 0
        week_count = week_users[0]['count'] if week_users else 0
        month_count = month_users[0]['count'] if month_users else 0
        
        text = (
            f"👥 User Statistics\n\n"
            f"📊 Total Users: {total_count}\n"
            f"📅 Today: {today_count}\n"
            f"📅 This Week: {week_count}\n"
            f"📅 This Month: {month_count}\n\n"
            f"📈 Growth Rate:\n"
            f"Weekly: +{week_count}\n"
            f"Monthly: +{month_count}"
        )
        
        await callback.message.edit_text(
            text,
            reply_markup=statistics_keyboard()
        )
    except Exception as e:
        await callback.message.edit_text(
            f"❌ Error loading user statistics: {str(e)}",
            reply_markup=statistics_keyboard()
        )
    
    await callback.answer()


@router.callback_query(lambda c: c.data == "order_stats")
async def show_order_statistics(callback: CallbackQuery):
    """Show order statistics."""
    # For now, showing placeholder data
    # In a real app, you'd have an orders table
    
    text = (
        f"🚗 Order Statistics\n\n"
        f"📊 Total Orders: 0\n"
        f"✅ Completed: 0\n"
        f"⏳ In Progress: 0\n"
        f"❌ Cancelled: 0\n\n"
        f"📅 Today: 0\n"
        f"📅 This Week: 0\n"
        f"📅 This Month: 0\n\n"
        f"💰 Revenue:\n"
        f"Today: $0\n"
        f"This Month: $0"
    )
    
    await callback.message.edit_text(
        text,
        reply_markup=statistics_keyboard()
    )
    await callback.answer()


@router.callback_query(lambda c: c.data == "system_stats")
async def show_system_statistics(callback: CallbackQuery):
    """Show system statistics."""
    try:
        # Get bot uptime and other system stats
        uptime = "Unknown"  # You can implement actual uptime tracking
        
        # Get database stats
        db_stats = await db.execute_query(
            "SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del "
            "FROM pg_stat_user_tables WHERE tablename = 'users'"
        )
        
        if db_stats:
            stats = db_stats[0]
            text = (
                f"🖥️ System Statistics\n\n"
                f"⏱️ Bot Uptime: {uptime}\n"
                f"🗄️ Database Status: Online\n\n"
                f"📊 Database Operations:\n"
                f"Inserts: {stats.get('n_tup_ins', 0)}\n"
                f"Updates: {stats.get('n_tup_upd', 0)}\n"
                f"Deletes: {stats.get('n_tup_del', 0)}\n\n"
                f"💾 Memory Usage: Normal\n"
                f"🔄 Cache Status: Active"
            )
        else:
            text = (
                f"🖥️ System Statistics\n\n"
                f"⏱️ Bot Uptime: {uptime}\n"
                f"🗄️ Database Status: Online\n"
                f"💾 Memory Usage: Normal\n"
                f"🔄 Cache Status: Active"
            )
        
        await callback.message.edit_text(
            text,
            reply_markup=statistics_keyboard()
        )
    except Exception as e:
        await callback.message.edit_text(
            f"❌ Error loading system statistics: {str(e)}",
            reply_markup=statistics_keyboard()
        )
    
    await callback.answer()


@router.callback_query(lambda c: c.data == "export_stats")
async def export_statistics(callback: CallbackQuery):
    """Export statistics to file."""
    try:
        # Generate statistics report
        users = await db.execute_query("SELECT * FROM users ORDER BY created_at DESC")
        
        if not users:
            await callback.message.edit_text(
                "❌ No data to export.",
                reply_markup=statistics_keyboard()
            )
            await callback.answer()
            return
        
        # Create CSV content
        csv_content = "User ID,Username,First Name,Last Name,Phone,Created At,Updated At\n"
        for user in users:
            csv_content += (
                f"{user['user_id']},"
                f"{user.get('username', '')},"
                f"{user.get('first_name', '')},"
                f"{user.get('last_name', '')},"
                f"{user.get('phone', '')},"
                f"{user['created_at']},"
                f"{user['updated_at']}\n"
            )
        
        # In a real implementation, you'd save this to a file and send it
        # For now, we'll just show a confirmation
        await callback.message.edit_text(
            f"📊 Statistics Export\n\n"
            f"✅ Export prepared with {len(users)} users\n"
            f"📁 File would be generated here",
            reply_markup=statistics_keyboard()
        )
        
    except Exception as e:
        await callback.message.edit_text(
            f"❌ Error exporting statistics: {str(e)}",
            reply_markup=statistics_keyboard()
        )
    
    await callback.answer()
