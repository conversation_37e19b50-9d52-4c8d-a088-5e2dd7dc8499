"""
Keyboard Usage Examples

This file demonstrates how to use the keyboards module in your TaxiBot.
"""

from keyboards.inline import (
    main_menu_keyboard,
    confirmation_keyboard,
    back_keyboard,
    pagination_keyboard,
    settings_keyboard,
    taxi_order_keyboard,
    order_status_keyboard,
    create_dynamic_keyboard,
    url_keyboard,
    contact_keyboard
)

from keyboards.reply import (
    phone_request_keyboard,
    location_request_keyboard,
    remove_keyboard,
    main_reply_keyboard,
    yes_no_keyboard,
    cancel_keyboard,
    taxi_types_keyboard,
    create_reply_keyboard,
    webapp_keyboard
)

from keyboards.builders import (
    InlineKeyboardBuilder,
    ReplyKeyboardBuilder,
    quick_inline,
    quick_reply
)


# Example usage in handlers:

async def example_inline_keyboards(message):
    """Examples of using inline keyboards."""
    
    # 1. Basic inline keyboards
    await message.answer("Main Menu:", reply_markup=main_menu_keyboard())
    
    # 2. Confirmation keyboard
    await message.answer(
        "Are you sure?", 
        reply_markup=confirmation_keyboard("yes_action", "no_action")
    )
    
    # 3. Back button
    await message.answer("Settings:", reply_markup=back_keyboard("main_menu"))
    
    # 4. Pagination
    current_page = 1
    total_pages = 5
    await message.answer(
        f"Page {current_page} of {total_pages}",
        reply_markup=pagination_keyboard(current_page, total_pages, "orders")
    )
    
    # 5. Dynamic keyboard
    buttons_data = [
        ("Option 1", "opt1"),
        ("Option 2", "opt2"),
        ("Option 3", "opt3"),
        ("Option 4", "opt4")
    ]
    await message.answer(
        "Choose option:",
        reply_markup=create_dynamic_keyboard(buttons_data, row_width=2, add_back_button=True)
    )
    
    # 6. URL keyboard
    await message.answer(
        "Visit our website:",
        reply_markup=url_keyboard("https://example.com", "🌐 Open Website")
    )


async def example_reply_keyboards(message):
    """Examples of using reply keyboards."""
    
    # 1. Phone request
    await message.answer(
        "Please share your phone:",
        reply_markup=phone_request_keyboard()
    )
    
    # 2. Location request
    await message.answer(
        "Share your location:",
        reply_markup=location_request_keyboard()
    )
    
    # 3. Main reply keyboard
    await message.answer(
        "Main menu:",
        reply_markup=main_reply_keyboard()
    )
    
    # 4. Yes/No keyboard
    await message.answer(
        "Do you agree?",
        reply_markup=yes_no_keyboard()
    )
    
    # 5. Custom reply keyboard
    buttons = [["Option A", "Option B"], ["Option C"], ["Cancel"]]
    await message.answer(
        "Choose:",
        reply_markup=create_reply_keyboard(buttons, one_time=True)
    )
    
    # 6. Remove keyboard
    await message.answer("Keyboard removed", reply_markup=remove_keyboard())


async def example_keyboard_builders(message):
    """Examples of using keyboard builders."""
    
    # 1. Inline keyboard builder
    builder = InlineKeyboardBuilder()
    builder.button("Button 1", "btn1")
    builder.button("Button 2", "btn2")
    builder.row()  # Start new row
    builder.button("Button 3", "btn3")
    builder.back_button("main")
    builder.pagination(1, 5, "page")
    
    await message.answer("Built keyboard:", reply_markup=builder.build())
    
    # 2. Reply keyboard builder
    reply_builder = ReplyKeyboardBuilder()
    reply_builder.add_row("Option 1", "Option 2")
    reply_builder.contact_button("📱 Share Phone")
    reply_builder.location_button("📍 Share Location")
    reply_builder.cancel_button()
    reply_builder.one_time(True).resize(True)
    
    await message.answer("Built reply keyboard:", reply_markup=reply_builder.build())
    
    # 3. Quick keyboards
    inline_kb = quick_inline(
        ("Button 1", "data1"),
        ("Button 2", "data2"),
        ("Button 3", "data3"),
        row_width=2
    )
    
    reply_kb = quick_reply("Opt 1", "Opt 2", "Opt 3", "Cancel", row_width=2)
    
    await message.answer("Quick inline:", reply_markup=inline_kb)
    await message.answer("Quick reply:", reply_markup=reply_kb)


async def example_taxi_specific_keyboards(message):
    """Examples of taxi-specific keyboards."""
    
    # 1. Taxi order keyboard
    await message.answer(
        "Choose taxi type:",
        reply_markup=taxi_order_keyboard()
    )
    
    # 2. Order status keyboard
    order_id = "12345"
    await message.answer(
        f"Order #{order_id} status:",
        reply_markup=order_status_keyboard(order_id)
    )
    
    # 3. Taxi types reply keyboard
    await message.answer(
        "Select taxi type:",
        reply_markup=taxi_types_keyboard()
    )


# Callback handlers examples:

async def handle_main_menu_callback(callback):
    """Handle main menu callbacks."""
    if callback.data == "order_taxi":
        await callback.message.edit_text(
            "Order taxi:",
            reply_markup=taxi_order_keyboard()
        )
    elif callback.data == "settings":
        await callback.message.edit_text(
            "Settings:",
            reply_markup=settings_keyboard()
        )
    await callback.answer()


async def handle_pagination_callback(callback):
    """Handle pagination callbacks."""
    if callback.data.startswith("page_"):
        page = int(callback.data.split("_")[1])
        total_pages = 5  # Get from your data
        
        await callback.message.edit_text(
            f"Page {page} content here...",
            reply_markup=pagination_keyboard(page, total_pages, "page")
        )
    await callback.answer()


# Tips for using keyboards:

"""
1. Inline Keyboards:
   - Use for navigation and actions
   - Can edit messages with new keyboards
   - Good for confirmations and menus

2. Reply Keyboards:
   - Use for frequent actions
   - Persistent by default
   - Good for main navigation
   - Can request contact/location

3. Keyboard Builders:
   - Use for dynamic keyboards
   - More flexible than static functions
   - Chain methods for easy construction

4. Best Practices:
   - Always provide back buttons in deep menus
   - Use clear, descriptive button texts
   - Limit buttons per row (2-3 max)
   - Use emojis for better UX
   - Remove reply keyboards when not needed
   - Handle all callback queries with callback.answer()

5. Error Handling:
   - Always wrap keyboard operations in try/except
   - Handle missing callback data gracefully
   - Validate user permissions before showing keyboards
"""
