from aiogram import Router
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from keyboards.inline.usr_inline import (
    taxi_direction_keyboard,
    taxi_people_count_keyboard,
    taxi_confirmation_keyboard,
    additional_info_keyboard
)
from keyboards.reply.usr_reply import (
    cancel_keyboard,
    phone_request_keyboard,
    location_request_keyboard,
    main_user_keyboard,
    remove_keyboard
)
from states.usr_states import TaxiOrderStates
from utils.database import db
from filters.custom_filters import IsUserFilter

router = Router()
router.message.filter(IsUserFilter())
router.callback_query.filter(IsUserFilter())


async def start_taxi_order(message: Message, state: FSMContext):
    """Start taxi ordering process."""
    await state.set_state(TaxiOrderStates.choosing_direction)

    await message.answer(
        "🚗 <b>Taxi Order</b>\n\n"
        "Please select your travel direction:",
        reply_markup=taxi_direction_keyboard()
    )

    # Show cancel keyboard
    await message.answer(
        "You can cancel anytime by pressing the Cancel button below:",
        reply_markup=cancel_keyboard()
    )


@router.callback_query(lambda c: c.data.startswith("direction_"))
async def direction_selected(callback: CallbackQuery, state: FSMContext):
    """Handle direction selection."""
    direction_data = callback.data.replace("direction_", "")

    # Store direction in state
    await state.update_data(direction=direction_data)
    await state.set_state(TaxiOrderStates.choosing_people_count)

    # Format direction for display
    if direction_data == "toshkent_beshariq":
        direction_text = "Toshkent → Beshariq"
    else:
        direction_text = "Beshariq → Toshkent"

    await callback.message.edit_text(
        f"🚗 <b>Taxi Order</b>\n\n"
        f"📍 Direction: {direction_text}\n\n"
        "👥 How many people will be traveling?",
        reply_markup=taxi_people_count_keyboard()
    )
    await callback.answer()


@router.callback_query(lambda c: c.data.startswith("people_"))
async def people_count_selected(callback: CallbackQuery, state: FSMContext):
    """Handle people count selection."""
    people_count = int(callback.data.split("_")[1])

    # Store people count in state
    await state.update_data(people_count=people_count)
    await state.set_state(TaxiOrderStates.waiting_for_phone)

    # Get current data for display
    data = await state.get_data()
    direction = data.get('direction', '')

    if direction == "toshkent_beshariq":
        direction_text = "Toshkent → Beshariq"
    else:
        direction_text = "Beshariq → Toshkent"

    await callback.message.edit_text(
        f"🚗 <b>Taxi Order</b>\n\n"
        f"📍 Direction: {direction_text}\n"
        f"👥 People: {people_count}\n\n"
        "📱 Please share your phone number or type it manually:"
    )

    # Send phone request keyboard as new message
    await callback.message.answer(
        "You can share your contact or type your phone number:",
        reply_markup=phone_request_keyboard()
    )
    await callback.answer()


@router.message(TaxiOrderStates.waiting_for_phone)
async def phone_received(message: Message, state: FSMContext):
    """Handle phone number input."""
    phone = None

    if message.contact:
        phone = message.contact.phone_number
    elif message.text and message.text != "❌ Cancel":
        # Validate phone number format (basic validation)
        phone_text = message.text.strip()
        if phone_text.startswith('+') or phone_text.isdigit():
            phone = phone_text
        else:
            await message.answer(
                "❌ Invalid phone number format.\n"
                "Please enter a valid phone number or share your contact:",
                reply_markup=phone_request_keyboard()
            )
            return
    else:
        return

    # Store phone and move to confirmation
    await state.update_data(phone=phone)
    await state.set_state(TaxiOrderStates.confirmation)

    # Show confirmation
    await show_taxi_confirmation(message, state)


async def show_taxi_confirmation(message: Message, state: FSMContext):
    """Show taxi order confirmation."""
    data = await state.get_data()
    direction = data.get('direction', '')
    people_count = data.get('people_count', 0)
    phone = data.get('phone', '')
    pickup_location = data.get('pickup_location', '')
    destination_location = data.get('destination_location', '')
    description = data.get('description', '')

    # Format direction
    if direction == "toshkent_beshariq":
        direction_text = "Toshkent → Beshariq"
    else:
        direction_text = "Beshariq → Toshkent"

    # Build confirmation text
    confirmation_text = (
        f"🚗 <b>Taxi Order Confirmation</b>\n\n"
        f"📍 <b>Direction:</b> {direction_text}\n"
        f"👥 <b>People:</b> {people_count}\n"
        f"📱 <b>Phone:</b> {phone}\n"
    )

    # Add optional information if provided
    if pickup_location:
        confirmation_text += f"📍 <b>Pickup:</b> {pickup_location}\n"
    if destination_location:
        confirmation_text += f"🎯 <b>Destination:</b> {destination_location}\n"
    if description:
        confirmation_text += f"📝 <b>Description:</b> {description}\n"

    confirmation_text += "\n✅ Please review your order and confirm:"

    await message.answer(
        confirmation_text,
        reply_markup=taxi_confirmation_keyboard()
    )


@router.callback_query(lambda c: c.data == "send_taxi_order")
async def send_taxi_order(callback: CallbackQuery, state: FSMContext):
    """Send the taxi order."""
    data = await state.get_data()
    user_id = callback.from_user.id

    # Create order in database
    order_id = await db.create_taxi_order(
        user_id=user_id,
        direction=data.get('direction', ''),
        people_count=data.get('people_count', 0),
        phone=data.get('phone', ''),
        pickup_location=data.get('pickup_location'),
        destination_location=data.get('destination_location'),
        description=data.get('description')
    )

    if order_id:
        await callback.message.edit_text(
            f"✅ <b>Order Sent Successfully!</b>\n\n"
            f"🆔 Order ID: #{order_id}\n\n"
            f"Your taxi order has been sent to our drivers.\n"
            f"You will be contacted shortly at {data.get('phone')}.\n\n"
            f"Thank you for using TaxiBot! 🚗"
        )

        # Clear state and return to main menu
        await state.clear()
        await callback.message.answer(
            "Choose another option:",
            reply_markup=main_user_keyboard()
        )
    else:
        await callback.message.edit_text(
            "❌ <b>Error</b>\n\n"
            "Failed to send your order. Please try again.",
            reply_markup=taxi_confirmation_keyboard()
        )

    await callback.answer()


@router.callback_query(lambda c: c.data == "additional_info")
async def additional_info_menu(callback: CallbackQuery, state: FSMContext):
    """Show additional info menu."""
    await callback.message.edit_text(
        "➕ <b>Additional Information</b>\n\n"
        "You can add optional information to help drivers find you:",
        reply_markup=additional_info_keyboard()
    )
    await callback.answer()


@router.message(lambda message: message.location)
async def handle_location(message: Message):
    """Handle location sharing."""
    latitude = message.location.latitude
    longitude = message.location.longitude
    
    await message.answer(
        f"📍 Location received!\n"
        f"Latitude: {latitude}\n"
        f"Longitude: {longitude}\n\n"
        "Your taxi will arrive shortly!",
        reply_markup=remove_keyboard()
    )


@router.callback_query(lambda c: c.data == "add_pickup_location")
async def add_pickup_location(callback: CallbackQuery, state: FSMContext):
    """Add pickup location."""
    await state.set_state(TaxiOrderStates.waiting_pickup_location)
    await callback.message.edit_text(
        "📍 <b>Pickup Location</b>\n\n"
        "Please send your pickup location or type the address:"
    )

    await callback.message.answer(
        "You can share your location or type the address:",
        reply_markup=location_request_keyboard()
    )
    await callback.answer()


@router.callback_query(lambda c: c.data == "add_destination")
async def add_destination(callback: CallbackQuery, state: FSMContext):
    """Add destination location."""
    await state.set_state(TaxiOrderStates.waiting_destination_location)
    await callback.message.edit_text(
        "🎯 <b>Destination Location</b>\n\n"
        "Please type your destination address:"
    )
    await callback.answer()


@router.callback_query(lambda c: c.data == "add_description")
async def add_description(callback: CallbackQuery, state: FSMContext):
    """Add description."""
    await state.set_state(TaxiOrderStates.waiting_description)
    await callback.message.edit_text(
        "📝 <b>Additional Description</b>\n\n"
        "Please type any additional information for the driver:"
    )
    await callback.answer()


# Handle additional info inputs
@router.message(TaxiOrderStates.waiting_pickup_location)
async def pickup_location_received(message: Message, state: FSMContext):
    """Handle pickup location input."""
    location_text = ""

    if message.location:
        location_text = f"Lat: {message.location.latitude}, Lon: {message.location.longitude}"
    elif message.text and message.text != "❌ Cancel":
        location_text = message.text.strip()
    else:
        return

    await state.update_data(pickup_location=location_text)
    await state.set_state(TaxiOrderStates.confirmation)

    await message.answer(
        f"✅ Pickup location added: {location_text}\n\n"
        "Returning to confirmation...",
        reply_markup=remove_keyboard()
    )

    await show_taxi_confirmation(message, state)


@router.callback_query(lambda c: c.data == "change_phone")
async def change_phone(callback: CallbackQuery):
    """Request phone number change."""
    await callback.message.answer(
        "📱 Please share your new phone number:",
        reply_markup=phone_request_keyboard()
    )
    await callback.answer()


@router.message(TaxiOrderStates.waiting_destination_location)
async def destination_location_received(message: Message, state: FSMContext):
    """Handle destination location input."""
    if message.text and message.text != "❌ Cancel":
        destination_text = message.text.strip()
        await state.update_data(destination_location=destination_text)
        await state.set_state(TaxiOrderStates.confirmation)

        await message.answer(
            f"✅ Destination added: {destination_text}\n\n"
            "Returning to confirmation...",
            reply_markup=remove_keyboard()
        )

        await show_taxi_confirmation(message, state)


@router.message(TaxiOrderStates.waiting_description)
async def description_received(message: Message, state: FSMContext):
    """Handle description input."""
    if message.text and message.text != "❌ Cancel":
        description_text = message.text.strip()
        await state.update_data(description=description_text)
        await state.set_state(TaxiOrderStates.confirmation)

        await message.answer(
            f"✅ Description added: {description_text}\n\n"
            "Returning to confirmation...",
            reply_markup=remove_keyboard()
        )

        await show_taxi_confirmation(message, state)


# Back navigation handlers
@router.callback_query(lambda c: c.data == "back_to_direction")
async def back_to_direction(callback: CallbackQuery, state: FSMContext):
    """Go back to direction selection."""
    await state.set_state(TaxiOrderStates.choosing_direction)
    await callback.message.edit_text(
        "🚗 <b>Taxi Order</b>\n\n"
        "Please select your travel direction:",
        reply_markup=taxi_direction_keyboard()
    )
    await callback.answer()


@router.callback_query(lambda c: c.data == "back_to_phone")
async def back_to_phone(callback: CallbackQuery, state: FSMContext):
    """Go back to phone input."""
    await state.set_state(TaxiOrderStates.waiting_for_phone)

    data = await state.get_data()
    direction = data.get('direction', '')
    people_count = data.get('people_count', 0)

    if direction == "toshkent_beshariq":
        direction_text = "Toshkent → Beshariq"
    else:
        direction_text = "Beshariq → Toshkent"

    await callback.message.edit_text(
        f"🚗 <b>Taxi Order</b>\n\n"
        f"📍 Direction: {direction_text}\n"
        f"👥 People: {people_count}\n\n"
        "📱 Please share your phone number or type it manually:"
    )

    await callback.message.answer(
        "You can share your contact or type your phone number:",
        reply_markup=phone_request_keyboard()
    )
    await callback.answer()


@router.callback_query(lambda c: c.data == "back_to_confirmation")
async def back_to_confirmation(callback: CallbackQuery, state: FSMContext):
    """Go back to confirmation."""
    await state.set_state(TaxiOrderStates.confirmation)
    await show_taxi_confirmation(callback.message, state)
    await callback.answer()
