from aiogram.types import Reply<PERSON><PERSON>boardMarkup, KeyboardButton


def main_reply_keyboard() -> ReplyKeyboardMarkup:
    """Create main reply keyboard."""
    keyboard = [
        [Keyboard<PERSON>utton(text="🚗 Order Taxi"), KeyboardButton(text="📍 My Location")],
        [<PERSON>boardButton(text="🕒 History"), KeyboardButton(text="⚙️ Settings")],
        [KeyboardButton(text="📞 Support")]
    ]
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True,
        persistent=True
    )


def taxi_types_keyboard() -> ReplyKeyboardMarkup:
    """Create taxi types keyboard."""
    keyboard = [
        [KeyboardButton(text="🚗 Economy"), KeyboardButton(text="🚙 Comfort")],
        [KeyboardButton(text="🚐 Business"), KeyboardButton(text="🏎️ Premium")],
        [KeyboardButton(text="❌ Cancel")]
    ]
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True,
        one_time_keyboard=True
    )
