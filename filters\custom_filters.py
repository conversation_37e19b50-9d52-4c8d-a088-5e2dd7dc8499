from aiogram.filters import <PERSON>Filter
from aiogram.types import Message, CallbackQuery
from loader import settings
from loader import db
from typing import Union


class IsAdminFilter(BaseFilter):
    """Filter for admin users."""

    def __init__(self, admin_ids: list = None):
        self.admin_ids = admin_ids or settings.ADMIN_IDS or []

    async def __call__(self, event: Union[Message, CallbackQuery]) -> bool:
        user_id = event.from_user.id
        return user_id in self.admin_ids


class IsUserFilter(BaseFilter):
    """Filter for regular users (not banned, not admin, not driver)."""

    async def __call__(self, event: Union[Message, CallbackQuery]) -> bool:
        user_id = event.from_user.id

        # Check if user is admin
        if settings.ADMIN_IDS and user_id in settings.ADMIN_IDS:
            return False

        # Check if user is banned
        user_data = await db.get_user(user_id)
        if user_data and user_data.get('is_banned', False):
            return False

        # Check if user is driver
        if user_data and user_data.get('user_type') == 'driver':
            return False

        return True


class IsDriverFilter(BaseFilter):
    """Filter for drivers (approved drivers only)."""

    async def __call__(self, event: Union[Message, CallbackQuery]) -> bool:
        user_id = event.from_user.id
        user_data = await db.get_user(user_id)

        if not user_data:
            return False

        return (user_data.get('user_type') == 'driver' and
                user_data.get('driver_status') == 'approved' and
                not user_data.get('is_banned', False))


class TextLengthFilter(BaseFilter):
    """Filter messages by text length."""

    def __init__(self, min_length: int = 0, max_length: int = 4096):
        self.min_length = min_length
        self.max_length = max_length

    async def __call__(self, message: Message) -> bool:
        if not message.text:
            return False
        return self.min_length <= len(message.text) <= self.max_length
