import asyncpg
import logging
from typing import Optional, List, Dict, Any
from config.settings import get_settings

logger = logging.getLogger(__name__)


class Database:
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self.settings = get_settings()

    async def connect(self):
        """Create database connection pool."""
        try:
            self.pool = await asyncpg.create_pool(
                self.settings.DATABASE_URL,
                min_size=1,
                max_size=10,
                command_timeout=60
            )
            logger.info("Database connected successfully")
            await self.create_tables()
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise

    async def disconnect(self):
        """Close database connection pool."""
        if self.pool:
            await self.pool.close()
            logger.info("Database disconnected")

    async def create_tables(self):
        """Create necessary tables."""
        query = """
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            user_id BIGINT UNIQUE NOT NULL,
            username <PERSON><PERSON><PERSON><PERSON>(255),
            first_name <PERSON><PERSON><PERSON><PERSON>(255),
            last_name <PERSON><PERSON><PERSON><PERSON>(255),
            phone VARCHAR(20),
            user_type VARCHAR(20) DEFAULT 'user',
            is_banned BO<PERSON><PERSON>N DEFAULT FALSE,
            driver_status VARCHAR(20) DEFAULT NULL,
            car_info TEXT DEFAULT NULL,
            license_info TEXT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS taxi_orders (
            id SERIAL PRIMARY KEY,
            user_id BIGINT NOT NULL,
            direction VARCHAR(50) NOT NULL,
            people_count INTEGER NOT NULL,
            phone VARCHAR(20) NOT NULL,
            pickup_location TEXT DEFAULT NULL,
            destination_location TEXT DEFAULT NULL,
            description TEXT DEFAULT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id)
        );

        CREATE TABLE IF NOT EXISTS delivery_orders (
            id SERIAL PRIMARY KEY,
            user_id BIGINT NOT NULL,
            direction VARCHAR(50) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            pickup_location TEXT DEFAULT NULL,
            destination_location TEXT DEFAULT NULL,
            description TEXT DEFAULT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id)
        );

        CREATE INDEX IF NOT EXISTS idx_users_user_id ON users(user_id);
        CREATE INDEX IF NOT EXISTS idx_taxi_orders_user_id ON taxi_orders(user_id);
        CREATE INDEX IF NOT EXISTS idx_delivery_orders_user_id ON delivery_orders(user_id);
        """

        async with self.pool.acquire() as conn:
            await conn.execute(query)
        logger.info("Tables created successfully")

    async def add_user(self, user_id: int, username: str = None,
                      first_name: str = None, last_name: str = None,
                      phone: str = None, user_type: str = 'user') -> bool:
        """Add or update user in database."""
        query = """
        INSERT INTO users (user_id, username, first_name, last_name, phone, user_type)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (user_id)
        DO UPDATE SET
            username = EXCLUDED.username,
            first_name = EXCLUDED.first_name,
            last_name = EXCLUDED.last_name,
            phone = COALESCE(EXCLUDED.phone, users.phone),
            user_type = COALESCE(EXCLUDED.user_type, users.user_type),
            updated_at = CURRENT_TIMESTAMP
        """

        try:
            async with self.pool.acquire() as conn:
                await conn.execute(query, user_id, username, first_name, last_name, phone, user_type)
            return True
        except Exception as e:
            logger.error(f"Error adding user {user_id}: {e}")
            return False

    async def get_user(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user from database."""
        query = "SELECT * FROM users WHERE user_id = $1"
        
        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(query, user_id)
                return dict(row) if row else None
        except Exception as e:
            logger.error(f"Error getting user {user_id}: {e}")
            return None

    async def create_taxi_order(self, user_id: int, direction: str, people_count: int,
                               phone: str, pickup_location: str = None,
                               destination_location: str = None, description: str = None) -> Optional[int]:
        """Create a new taxi order."""
        query = """
        INSERT INTO taxi_orders (user_id, direction, people_count, phone, pickup_location, destination_location, description)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id
        """

        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(query, user_id, direction, people_count, phone,
                                        pickup_location, destination_location, description)
                return row['id'] if row else None
        except Exception as e:
            logger.error(f"Error creating taxi order for user {user_id}: {e}")
            return None

    async def create_delivery_order(self, user_id: int, direction: str, phone: str,
                                   pickup_location: str = None, destination_location: str = None,
                                   description: str = None) -> Optional[int]:
        """Create a new delivery order."""
        query = """
        INSERT INTO delivery_orders (user_id, direction, phone, pickup_location, destination_location, description)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id
        """

        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(query, user_id, direction, phone,
                                        pickup_location, destination_location, description)
                return row['id'] if row else None
        except Exception as e:
            logger.error(f"Error creating delivery order for user {user_id}: {e}")
            return None

    async def update_user_type(self, user_id: int, user_type: str,
                              driver_status: str = None, car_info: str = None,
                              license_info: str = None) -> bool:
        """Update user type and driver information."""
        query = """
        UPDATE users
        SET user_type = $2, driver_status = $3, car_info = $4, license_info = $5, updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $1
        """

        try:
            async with self.pool.acquire() as conn:
                await conn.execute(query, user_id, user_type, driver_status, car_info, license_info)
            return True
        except Exception as e:
            logger.error(f"Error updating user type for {user_id}: {e}")
            return False

    async def execute_query(self, query: str, *args) -> List[Dict[str, Any]]:
        """Execute custom query."""
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(query, *args)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            return []
