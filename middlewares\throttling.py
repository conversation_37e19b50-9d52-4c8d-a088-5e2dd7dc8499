from typing import Callable, Dict, Any, Awaitable
from aiogram import BaseMiddleware
from aiogram.types import Message, CallbackQuery
import time
import asyncio


class AdvancedThrottlingMiddleware(BaseMiddleware):
    """Advanced throttling with user notifications and cleanup."""
    
    def __init__(self, limit: float = 1.0, notify_user: bool = True, cleanup_interval: int = 300):
        self.limit = limit
        self.notify_user = notify_user
        self.cleanup_interval = cleanup_interval
        self.storage: Dict[int, Dict[str, Any]] = {}
        self.warned_users: Dict[int, float] = {}
        
        # Start cleanup task
        asyncio.create_task(self._cleanup_task())

    async def __call__(
        self,
        handler: Callable,
        event: Message | CallbackQuery,
        data: Dict[str, Any]
    ) -> Any:
        user_id = event.from_user.id
        current_time = time.time()
        
        # Initialize user data if not exists
        if user_id not in self.storage:
            self.storage[user_id] = {
                'last_message': 0,
                'message_count': 0,
                'first_message': current_time
            }
        
        user_data = self.storage[user_id]
        time_diff = current_time - user_data['last_message']
        
        # Check if user is throttled
        if time_diff < self.limit:
            user_data['message_count'] += 1
            
            # Notify user if enabled and not recently warned
            if (self.notify_user and 
                user_id not in self.warned_users or 
                current_time - self.warned_users.get(user_id, 0) > 10):
                
                await self._notify_user(event, time_diff)
                self.warned_users[user_id] = current_time
            
            return  # Skip handling
        
        # Update user data
        user_data['last_message'] = current_time
        user_data['message_count'] = 0
        
        return await handler(event, data)
    
    async def _notify_user(self, event: Message | CallbackQuery, time_diff: float):
        """Notify user about throttling."""
        wait_time = self.limit - time_diff
        message = f"⏳ Please wait {wait_time:.1f} seconds before sending another message."
        
        try:
            if isinstance(event, Message):
                await event.answer(message)
            elif isinstance(event, CallbackQuery):
                await event.answer(message, show_alert=True)
        except Exception:
            pass  # Ignore if can't send notification
    
    async def _cleanup_task(self):
        """Cleanup old user data periodically."""
        while True:
            await asyncio.sleep(self.cleanup_interval)
            current_time = time.time()
            
            # Remove users inactive for more than 1 hour
            inactive_users = [
                user_id for user_id, data in self.storage.items()
                if current_time - data['last_message'] > 3600
            ]
            
            for user_id in inactive_users:
                del self.storage[user_id]
                if user_id in self.warned_users:
                    del self.warned_users[user_id]