from aiogram import Router
from aiogram.filters import Command
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from filters.custom_filters import IsAdminFilter
from keyboards.inline.adm_inline import admin_main_keyboard
from loader import db

router = Router()
router.message.filter(IsAdminFilter())
router.callback_query.filter(IsAdminFilter())


@router.message(Command("admin"))
async def admin_panel(message: Message, state: FSMContext):
    """Show admin panel."""
    await state.clear()
    
    await message.answer(
        "🔧 Admin Panel\n\n"
        "Welcome to the admin dashboard.",
        reply_markup=admin_main_keyboard()
    )


@router.callback_query(lambda c: c.data == "admin_main")
async def show_admin_main(callback: CallbackQuery):
    """Show admin main menu."""
    await callback.message.edit_text(
        "🔧 Admin Panel\n\n"
        "Choose an action:",
        reply_markup=admin_main_keyboard()
    )
    await callback.answer()


@router.callback_query(lambda c: c.data == "admin_stats")
async def show_admin_stats(callback: CallbackQuery):
    """Show admin statistics."""
    # Get statistics from database
    users_count = len(await db.execute_query("SELECT COUNT(*) as count FROM users"))
    
    await callback.message.edit_text(
        f"📊 Statistics\n\n"
        f"👥 Total Users: {users_count}\n"
        f"🚗 Active Orders: 0\n"
        f"✅ Completed Orders: 0\n"
        f"💰 Total Revenue: $0",
        reply_markup=admin_main_keyboard()
    )
    await callback.answer()


@router.callback_query(lambda c: c.data == "broadcast_message")
async def broadcast_message(callback: CallbackQuery, state: FSMContext):
    """Start broadcast message process."""
    await callback.message.edit_text(
        "📢 Broadcast Message\n\n"
        "Send the message you want to broadcast to all users:",
    )
    await state.set_state("waiting_broadcast_message")
    await callback.answer()


@router.message(lambda message: True)
async def handle_broadcast_message(message: Message, state: FSMContext):
    """Handle broadcast message."""
    current_state = await state.get_state()
    if current_state == "waiting_broadcast_message":
        # Get all users
        users = await db.execute_query("SELECT user_id FROM users")
        
        sent_count = 0
        failed_count = 0
        
        for user in users:
            try:
                await message.bot.send_message(
                    chat_id=user['user_id'],
                    text=f"📢 Broadcast Message:\n\n{message.text}"
                )
                sent_count += 1
            except Exception:
                failed_count += 1
        
        await message.answer(
            f"📢 Broadcast Complete\n\n"
            f"✅ Sent: {sent_count}\n"
            f"❌ Failed: {failed_count}",
            reply_markup=admin_main_keyboard()
        )
        await state.clear()
