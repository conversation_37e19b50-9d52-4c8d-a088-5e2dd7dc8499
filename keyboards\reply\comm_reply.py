from aiogram.types import (
    Reply<PERSON><PERSON>boardMarkup, 
    KeyboardButton, 
    Reply<PERSON><PERSON>boardRemove,
    WebApp
)
from typing import List, Optional


def phone_request_keyboard() -> ReplyKeyboardMarkup:
    """Create keyboard requesting phone number."""
    keyboard = [
        [KeyboardButton(text="📱 Share Phone Number", request_contact=True)],
        [KeyboardButton(text="❌ Cancel")]
    ]
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True,
        one_time_keyboard=True
    )


def location_request_keyboard() -> ReplyKeyboardMarkup:
    """Create keyboard requesting location."""
    keyboard = [
        [KeyboardButton(text="📍 Share Location", request_location=True)],
        [KeyboardButton(text="❌ Cancel")]
    ]
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True,
        one_time_keyboard=True
    )


def remove_keyboard() -> ReplyKeyboardRemove:
    """Remove reply keyboard."""
    return ReplyKeyboardRemove()


def yes_no_keyboard() -> ReplyKeyboardMarkup:
    """Create simple yes/no keyboard."""
    keyboard = [
        [KeyboardButton(text="✅ Yes"), KeyboardButton(text="❌ No")]
    ]
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True,
        one_time_keyboard=True
    )


def cancel_keyboard() -> ReplyKeyboardMarkup:
    """Create cancel keyboard."""
    keyboard = [
        [KeyboardButton(text="❌ Cancel")]
    ]
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True,
        one_time_keyboard=True
    )


def create_reply_keyboard(
    buttons: List[List[str]], 
    resize: bool = True,
    one_time: bool = False,
    persistent: bool = False,
    request_contact: bool = False,
    request_location: bool = False
) -> ReplyKeyboardMarkup:
    """Create custom reply keyboard."""
    keyboard = []
    
    for row in buttons:
        keyboard_row = []
        for button_text in row:
            keyboard_row.append(
                KeyboardButton(
                    text=button_text,
                    request_contact=request_contact and "phone" in button_text.lower(),
                    request_location=request_location and "location" in button_text.lower()
                )
            )
        keyboard.append(keyboard_row)
    
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=resize,
        one_time_keyboard=one_time,
        persistent=persistent
    )


def webapp_keyboard(webapp_url: str, button_text: str = "Open App") -> ReplyKeyboardMarkup:
    """Create keyboard with web app button."""
    keyboard = [
        [KeyboardButton(text=button_text, web_app=WebApp(url=webapp_url))]
    ]
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True
    )
