from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from typing import List, Tuple, Optional


def back_keyboard(back_data: str = "back") -> InlineKeyboardMarkup:
    """Create back button keyboard."""
    buttons = [
        [InlineKeyboardButton(text="🔙 Back", callback_data=back_data)]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def pagination_keyboard(
    current_page: int, 
    total_pages: int, 
    data_prefix: str = "page"
) -> InlineKeyboardMarkup:
    """Create pagination keyboard."""
    buttons = []
    
    # Navigation buttons
    nav_buttons = []
    if current_page > 1:
        nav_buttons.append(InlineKeyboardButton(text="◀️", callback_data=f"{data_prefix}_{current_page - 1}"))
    
    nav_buttons.append(InlineKeyboardButton(text=f"{current_page}/{total_pages}", callback_data="current_page"))
    
    if current_page < total_pages:
        nav_buttons.append(InlineKeyboardButton(text="▶️", callback_data=f"{data_prefix}_{current_page + 1}"))
    
    if nav_buttons:
        buttons.append(nav_buttons)
    
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def create_dynamic_keyboard(
    buttons_data: List[Tuple[str, str]], 
    row_width: int = 2,
    add_back_button: bool = False
) -> InlineKeyboardMarkup:
    """Create dynamic inline keyboard from button data."""
    keyboard = []
    row = []
    
    for text, callback_data in buttons_data:
        row.append(InlineKeyboardButton(text=text, callback_data=callback_data))
        
        if len(row) >= row_width:
            keyboard.append(row)
            row = []
    
    if row:
        keyboard.append(row)
    
    if add_back_button:
        keyboard.append([InlineKeyboardButton(text="🔙 Back", callback_data="back")])
    
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def url_keyboard(url: str, text: str = "Open Link") -> InlineKeyboardMarkup:
    """Create keyboard with URL button."""
    buttons = [
        [InlineKeyboardButton(text=text, url=url)]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def contact_keyboard(phone_number: str, text: str = "📞 Call") -> InlineKeyboardMarkup:
    """Create keyboard with contact button.""" 
    buttons = [
        [InlineKeyboardButton(text=text, callback_data=f"contact_{phone_number}")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)
