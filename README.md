# 🚗 TaxiBot

A modern Telegram bot for taxi and delivery services between Toshkent and Beshariq, built with aiogram 3.x and PostgreSQL.

## ✨ Features

### 👤 **User Features**
- 🚗 **Taxi Ordering**: Complete flow with direction, people count, and phone number
- 📦 **Delivery Service**: Full delivery ordering with pickup/destination addresses
- 🚙 **Driver Registration**: Apply to become a driver with vehicle and license info
- 👤 **Profile Management**: View and manage user profile
- 📞 **Contact Support**: Easy access to support information

### 👑 **Admin Features**
- 👥 **User Management**: Ban/unban users, view user details
- 📊 **Statistics**: Comprehensive analytics and reporting
- 🚙 **Driver Management**: Approve/reject driver applications
- 📢 **Broadcasting**: Send messages to all users

### 🛡️ **Security & UX**
- **3 User Types**: Users, Drivers, Admins with proper filtering
- **State Management**: Bulletproof FSM with state-specific handlers
- **Fallback System**: Graceful handling of unexpected inputs
- **Cancel Anytime**: Users can cancel operations at any step
- **Context Help**: State-aware assistance messages

## 🏗️ Architecture

### **Project Structure**
```
taxibot/
├── handlers/
│   ├── user/           # User-specific handlers
│   │   ├── usr_base.py # Main menu and basic actions
│   │   ├── taxi.py     # Taxi ordering flow
│   │   ├── delivery.py # Delivery ordering flow
│   │   └── driver.py   # Driver registration
│   ├── admin/          # Admin panel handlers
│   └── fallback.py     # Unhandled request handler
├── keyboards/
│   ├── inline/         # Inline keyboards
│   ├── reply/          # Reply keyboards
│   └── builders.py     # Keyboard builders
├── states/
│   └── usr_states.py   # FSM states
├── filters/
│   └── custom_filters.py # User type filters
├── utils/
│   ├── database.py     # Database operations
│   └── helpers.py      # Utility functions
├── config/
│   └── settings.py     # Configuration
├── middlewares/
│   └── throttling.py   # Rate limiting
├── main.py             # Entry point
├── loader.py           # Bot initialization
└── requirements.txt    # Dependencies
```

### **Technology Stack**
- **Framework**: aiogram 3.x (Modern Telegram Bot API)
- **Database**: PostgreSQL with asyncpg
- **Language**: Python 3.8+
- **Architecture**: Modular with clean separation of concerns

## 🚀 Quick Start

### **1. Prerequisites**
- Python 3.8+
- PostgreSQL database
- Telegram Bot Token (from @BotFather)

### **2. Installation**
```bash
# Clone the repository
git clone <your-repo-url>
cd taxibot

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### **3. Configuration**
Create a `.env` file in the project root:
```env
BOT_TOKEN=your_bot_token_here
DATABASE_URL=postgresql://username:password@localhost:5432/taxibot
ADMIN_IDS=*********,*********
DEBUG=False
```

### **4. Database Setup**
```bash
# Create PostgreSQL database
createdb taxibot

# The bot will automatically create tables on first run
```

### **5. Run the Bot**
```bash
python main.py
```

## 📱 User Flow

### **🚗 Taxi Ordering**
1. User selects "🚗 Taxi" from main menu
2. Chooses direction: Toshkent ↔ Beshariq
3. Selects number of people (1-4)
4. Provides phone number (contact or manual)
5. Reviews order details
6. Optionally adds pickup location, destination, description
7. Confirms and sends order

### **📦 Delivery Ordering**
1. User selects "📦 Delivery" from main menu
2. Chooses direction: Toshkent ↔ Beshariq
3. Provides phone number
4. Enters pickup address
5. Enters destination address
6. Describes the item
7. Confirms and sends order

### **🚙 Driver Registration**
1. User selects "🚙 Join Drivers" from main menu
2. Provides phone number
3. Enters vehicle information
4. Provides license details
5. Submits application for review

## 🛡️ Security Features

### **User Type Filtering**
- **IsUserFilter**: Regular users (not banned, not admin, not driver)
- **IsDriverFilter**: Approved drivers only
- **IsAdminFilter**: Admin users only

### **State Management**
- All handlers have state filters to prevent unexpected behavior
- Users can only access actions appropriate to their current state
- Comprehensive fallback system for unhandled requests

### **Input Validation**
- Phone number format validation
- Required field checking
- Sanitized database inputs

## 🗄️ Database Schema

### **Users Table**
```sql
users (
    id SERIAL PRIMARY KEY,
    user_id BIGINT UNIQUE NOT NULL,
    username VARCHAR(255),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    phone VARCHAR(20),
    user_type VARCHAR(20) DEFAULT 'user',
    is_banned BOOLEAN DEFAULT FALSE,
    driver_status VARCHAR(20) DEFAULT NULL,
    car_info TEXT DEFAULT NULL,
    license_info TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **Orders Tables**
- **taxi_orders**: Taxi order details with direction, people count, locations
- **delivery_orders**: Delivery order details with pickup/destination addresses

## 🔧 Development

### **Adding New Features**
1. Create handlers in appropriate directory (`handlers/user/`, `handlers/admin/`)
2. Add states to `states/usr_states.py` if needed
3. Create keyboards in `keyboards/inline/` or `keyboards/reply/`
4. Add database methods to `utils/database.py`
5. Update router registration in `handlers/__init__.py`

### **Code Style**
- Use aiogram.F syntax for filters: `F.text == "value"`
- Add state filters to prevent unexpected behavior
- Follow modular architecture principles
- Use type hints and docstrings

### **Testing**
```bash
# Run the bot in debug mode
DEBUG=True python main.py

# Test different user flows
# Check logs for any unhandled requests
```

## 📝 Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `BOT_TOKEN` | Telegram Bot Token | Yes | - |
| `DATABASE_URL` | PostgreSQL connection string | Yes | - |
| `ADMIN_IDS` | Comma-separated admin user IDs | No | - |
| `DEBUG` | Enable debug mode | No | False |

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation

---

**Built with ❤️ using aiogram 3.x**
