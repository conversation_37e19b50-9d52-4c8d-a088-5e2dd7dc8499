from aiogram import Router
from aiogram.filters import CommandStart
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from utils.database import db
from keyboards.inline.main import main_menu_keyboard
from keyboards.inline.settings import settings_keyboard

router = Router()


@router.message(CommandStart())
async def cmd_start(message: Message, state: FSMContext):
    """Handle /start command."""
    await state.clear()
    
    # Save user to database
    user = message.from_user
    await db.add_user(
        user_id=user.id,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name
    )
    
    await message.answer(
        f"Hello, {message.from_user.full_name}!\n"
        "Welcome to the TaxiBot! 🚗\n\n"
        "Choose an option from the menu below:",
        reply_markup=main_menu_keyboard()
    )


@router.callback_query(lambda c: c.data == "main_menu")
async def show_main_menu(callback: CallbackQuery):
    """Show main menu."""
    await callback.message.edit_text(
        "🚗 TaxiBot Main Menu\n\n"
        "Choose an option:",
        reply_markup=main_menu_keyboard()
    )
    await callback.answer()


@router.callback_query(lambda c: c.data == "settings")
async def show_settings(callback: CallbackQuery):
    """Show settings menu."""
    await callback.message.edit_text(
        "⚙️ Settings\n\n"
        "Configure your preferences:",
        reply_markup=settings_keyboard()
    )
    await callback.answer()
