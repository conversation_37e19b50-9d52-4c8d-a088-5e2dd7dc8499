import asyncio
import logging

from loader import bot, dp, db
from handlers import setup_routers
from middlewares import setup_middlewares


async def main():
    """Main function to start the bot."""
    # Initialize database
    await db.connect()
    
    # Drop pending updates by deleting webhook
    await bot.delete_webhook(drop_pending_updates=True)
    logging.info("Dropped pending updates")
    
    # Setup middlewares
    setup_middlewares(dp)
    
    # Setup routers
    setup_routers(dp)
    
    try:
        # Start polling
        await dp.start_polling(bot)
    finally:
        # Cleanup
        await db.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
