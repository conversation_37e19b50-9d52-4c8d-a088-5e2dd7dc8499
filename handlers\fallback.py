from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from keyboards.reply.usr_reply import main_user_keyboard, remove_keyboard
from filters.custom_filters import IsUser<PERSON>ilter, IsDriverFilter, IsAdminFilter
import logging

logger = logging.getLogger(__name__)

# Create router with lowest priority
router = Router()


@router.callback_query(IsUserFilter())
async def unhandled_user_callback(callback: CallbackQuery, state: FSMContext):
    """Handle unhandled callback queries from users."""
    current_state = await state.get_state()
    
    logger.warning(f"Unhandled callback from user {callback.from_user.id}: {callback.data} in state {current_state}")
    
    await callback.answer("❌ This action is not available right now.")
    
    # If user is in a state, offer to return to main menu
    if current_state:
        await callback.message.answer(
            "🔄 It seems you're in the middle of an operation.\n"
            "Would you like to return to the main menu?",
            reply_markup=main_user_keyboard()
        )
        await state.clear()
    else:
        await callback.message.answer(
            "Please use the menu options below:",
            reply_markup=main_user_keyboard()
        )


@router.message(IsUserFilter())
async def unhandled_user_message(message: Message, state: FSMContext):
    """Handle unhandled messages from users."""
    current_state = await state.get_state()
    
    logger.warning(f"Unhandled message from user {message.from_user.id}: {message.text} in state {current_state}")
    
    # Check if user sent a message while in a specific state
    if current_state:
        # Check if it's a cancel message
        if message.text == "❌ Cancel":
            await state.clear()
            await message.answer(
                "❌ Operation cancelled.\n\n"
                "Choose an option from the menu:",
                reply_markup=main_user_keyboard()
            )
            return
        
        # For other messages in states, provide context-specific help
        state_help_messages = {
            "TaxiOrderStates:waiting_for_phone": (
                "📱 Please share your phone number using the button below or type it manually.\n"
                "Format: +998********* or *********"
            ),
            "TaxiOrderStates:waiting_pickup_location": (
                "📍 Please share your location using the button or type your pickup address."
            ),
            "TaxiOrderStates:waiting_destination_location": (
                "🎯 Please type your destination address."
            ),
            "TaxiOrderStates:waiting_description": (
                "📝 Please type any additional information for the driver."
            ),
            "DeliveryOrderStates:waiting_for_phone": (
                "📱 Please share your phone number using the button below or type it manually."
            ),
            "DeliveryOrderStates:waiting_pickup_location": (
                "📍 Please type the pickup address where we should collect the item."
            ),
            "DeliveryOrderStates:waiting_destination_location": (
                "🎯 Please type the destination address where we should deliver the item."
            ),
            "DeliveryOrderStates:waiting_description": (
                "📝 Please describe what you want to deliver (size, weight, special handling, etc.)."
            ),
            "DriverRegistrationStates:waiting_for_phone": (
                "📱 Please share your phone number using the button below or type it manually."
            ),
            "DriverRegistrationStates:waiting_for_car_info": (
                "🚗 Please provide your vehicle information:\n"
                "• Make and model\n• Year\n• Color\n• License plate number"
            ),
            "DriverRegistrationStates:waiting_for_license": (
                "📄 Please provide your driver's license information:\n"
                "• License number\n• Issue date\n• Expiry date\n• Category"
            )
        }
        
        help_message = state_help_messages.get(current_state, 
            "❓ I didn't understand that. Please follow the instructions above or cancel the operation.")
        
        await message.answer(help_message)
    else:
        # User sent an unexpected message while not in any state
        await message.answer(
            "❓ I didn't understand that command.\n\n"
            "Please use the menu options below:",
            reply_markup=main_user_keyboard()
        )


@router.callback_query(IsDriverFilter())
async def unhandled_driver_callback(callback: CallbackQuery, state: FSMContext):
    """Handle unhandled callback queries from drivers."""
    logger.warning(f"Unhandled callback from driver {callback.from_user.id}: {callback.data}")
    
    await callback.answer("❌ This action is not available.")
    await callback.message.answer(
        "🚙 Driver features are coming soon!\n"
        "For now, please contact support for assistance."
    )


@router.message(IsDriverFilter())
async def unhandled_driver_message(message: Message, state: FSMContext):
    """Handle unhandled messages from drivers."""
    logger.warning(f"Unhandled message from driver {message.from_user.id}: {message.text}")
    
    await message.answer(
        "🚙 Driver features are coming soon!\n"
        "For now, please contact support for assistance."
    )


@router.callback_query(IsAdminFilter())
async def unhandled_admin_callback(callback: CallbackQuery, state: FSMContext):
    """Handle unhandled callback queries from admins."""
    logger.warning(f"Unhandled callback from admin {callback.from_user.id}: {callback.data}")
    
    await callback.answer("❌ This admin action is not available.")


@router.message(IsAdminFilter())
async def unhandled_admin_message(message: Message, state: FSMContext):
    """Handle unhandled messages from admins."""
    logger.warning(f"Unhandled message from admin {message.from_user.id}: {message.text}")
    
    await message.answer("❓ Unknown admin command. Use /admin to access the admin panel.")


# Catch-all for any other messages/callbacks (banned users, etc.)
@router.callback_query()
async def unhandled_callback_final(callback: CallbackQuery, state: FSMContext):
    """Final catch-all for unhandled callbacks."""
    logger.warning(f"Unhandled callback from user {callback.from_user.id}: {callback.data} (possibly banned or unknown user type)")
    
    await callback.answer("❌ Access denied.")


@router.message()
async def unhandled_message_final(message: Message, state: FSMContext):
    """Final catch-all for unhandled messages."""
    logger.warning(f"Unhandled message from user {message.from_user.id}: {message.text} (possibly banned or unknown user type)")
    
    # Check if user might be banned
    from utils.database import db
    user_data = await db.get_user(message.from_user.id)
    
    if user_data and user_data.get('is_banned', False):
        await message.answer(
            "🚫 Your account has been suspended.\n"
            "Please contact support for assistance."
        )
    else:
        await message.answer(
            "❓ I don't understand. Please start with /start"
        )
