import os
from dataclasses import dataclass
from typing import Optional, List
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


@dataclass
class Settings:
    BOT_TOKEN: str
    DATABASE_URL: str = "postgresql://user:password@localhost/taxibot"
    DEBUG: bool = False
    ADMIN_IDS: List[int] = None


def get_settings() -> Settings:
    """Get application settings from environment variables."""
    # Parse admin IDs from environment variable
    admin_ids_str = os.getenv("ADMIN_IDS", "")
    admin_ids = []
    if admin_ids_str:
        try:
            admin_ids = [int(id.strip()) for id in admin_ids_str.split(",") if id.strip()]
        except ValueError:
            print("Warning: Invalid ADMIN_IDS format in environment variables")
            admin_ids = []
    
    return Settings(
        BOT_TOKEN=os.getenv("BOT_TOKEN", ""),
        DATABASE_URL=os.getenv("DATABASE_URL", "postgresql://user:password@localhost/taxibot"),
        DEBUG=os.getenv("DEBUG", "False").lower() == "true",
        ADMIN_IDS=admin_ids
    )


def is_admin(user_id: int) -> bool:
    """Check if user is admin."""
    settings = get_settings()
    return user_id in settings.ADMIN_IDS if settings.ADMIN_IDS else False
