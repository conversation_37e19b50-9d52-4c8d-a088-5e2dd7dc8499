from aiogram import Router
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from filters.custom_filters import IsAdminFilter
from keyboards.inline.adm_inline import user_management_keyboard, user_actions_keyboard
from keyboards.builders import InlineKeyboardBuilder
from loader import db, settings


# Get admin IDs from settings

ADMIN_IDS = settings.ADMIN_IDS

router = Router()
router.message.filter(IsAdminFilter(ADMIN_IDS))
router.callback_query.filter(IsAdminFilter(ADMIN_IDS))


@router.callback_query(lambda c: c.data == "user_management")
async def show_user_management(callback: CallbackQuery):
    """Show user management menu."""
    await callback.message.edit_text(
        "👥 User Management\n\n"
        "Choose an action:",
        reply_markup=user_management_keyboard()
    )
    await callback.answer()


@router.callback_query(lambda c: c.data == "list_users")
async def list_users(callback: Callback<PERSON>uery):
    """List all users."""
    users = await db.execute_query(
        "SELECT user_id, username, first_name, last_name, created_at FROM users ORDER BY created_at DESC LIMIT 10"
    )
    
    if not users:
        await callback.message.edit_text(
            "👥 No users found.",
            reply_markup=user_management_keyboard()
        )
        await callback.answer()
        return
    
    text = "👥 Recent Users:\n\n"
    for user in users:
        username = user.get('username', 'N/A')
        name = f"{user.get('first_name', '')} {user.get('last_name', '')}".strip()
        text += f"ID: {user['user_id']}\n"
        text += f"Username: @{username}\n"
        text += f"Name: {name}\n"
        text += f"Joined: {user['created_at'].strftime('%Y-%m-%d')}\n\n"
    
    await callback.message.edit_text(
        text,
        reply_markup=user_management_keyboard()
    )
    await callback.answer()


@router.callback_query(lambda c: c.data == "search_user")
async def search_user_prompt(callback: CallbackQuery, state: FSMContext):
    """Prompt for user search."""
    await callback.message.edit_text(
        "🔍 Search User\n\n"
        "Send user ID or username to search:"
    )
    await state.set_state("waiting_user_search")
    await callback.answer()


@router.message(lambda message: True)
async def handle_user_search(message: Message, state: FSMContext):
    """Handle user search."""
    current_state = await state.get_state()
    if current_state == "waiting_user_search":
        search_term = message.text.strip()
        
        # Try to search by user_id first
        if search_term.isdigit():
            user = await db.get_user(int(search_term))
        else:
            # Search by username
            users = await db.execute_query(
                "SELECT * FROM users WHERE username ILIKE $1 LIMIT 1",
                f"%{search_term}%"
            )
            user = users[0] if users else None
        
        if user:
            username = user.get('username', 'N/A')
            name = f"{user.get('first_name', '')} {user.get('last_name', '')}".strip()
            phone = user.get('phone', 'N/A')
            
            # Create keyboard with user actions
            builder = InlineKeyboardBuilder()
            builder.button("🚫 Ban User", f"ban_user_{user['user_id']}")
            builder.button("✅ Unban User", f"unban_user_{user['user_id']}")
            builder.row()
            builder.button("📱 Contact User", f"contact_user_{user['user_id']}")
            builder.back_button("user_management")
            
            await message.answer(
                f"👤 User Found:\n\n"
                f"ID: {user['user_id']}\n"
                f"Username: @{username}\n"
                f"Name: {name}\n"
                f"Phone: {phone}\n"
                f"Joined: {user['created_at'].strftime('%Y-%m-%d %H:%M')}\n"
                f"Last Update: {user['updated_at'].strftime('%Y-%m-%d %H:%M')}",
                reply_markup=builder.build()
            )
        else:
            await message.answer(
                "❌ User not found.",
                reply_markup=user_management_keyboard()
            )
        
        await state.clear()


@router.callback_query(lambda c: c.data.startswith("ban_user_"))
async def ban_user(callback: CallbackQuery):
    """Ban a user."""
    user_id = int(callback.data.split("_")[-1])
    
    # Here you would implement actual banning logic
    # For now, we'll just show a confirmation
    
    await callback.message.edit_text(
        f"🚫 User {user_id} has been banned.",
        reply_markup=user_management_keyboard()
    )
    await callback.answer("User banned successfully!")


@router.callback_query(lambda c: c.data.startswith("unban_user_"))
async def unban_user(callback: CallbackQuery):
    """Unban a user."""
    user_id = int(callback.data.split("_")[-1])
    
    # Here you would implement actual unbanning logic
    # For now, we'll just show a confirmation
    
    await callback.message.edit_text(
        f"✅ User {user_id} has been unbanned.",
        reply_markup=user_management_keyboard()
    )
    await callback.answer("User unbanned successfully!")


@router.callback_query(lambda c: c.data.startswith("contact_user_"))
async def contact_user(callback: CallbackQuery, state: FSMContext):
    """Contact a user."""
    user_id = int(callback.data.split("_")[-1])
    
    await callback.message.edit_text(
        f"📱 Contact User {user_id}\n\n"
        "Send the message you want to send to this user:"
    )
    await state.set_state(f"waiting_contact_message_{user_id}")
    await callback.answer()


@router.message(lambda message: True)
async def handle_contact_message(message: Message, state: FSMContext):
    """Handle contact message to user."""
    current_state = await state.get_state()
    if current_state and current_state.startswith("waiting_contact_message_"):
        user_id = int(current_state.split("_")[-1])
        
        try:
            await message.bot.send_message(
                chat_id=user_id,
                text=f"📢 Message from Admin:\n\n{message.text}"
            )
            await message.answer(
                f"✅ Message sent to user {user_id}",
                reply_markup=user_management_keyboard()
            )
        except Exception as e:
            await message.answer(
                f"❌ Failed to send message to user {user_id}: {str(e)}",
                reply_markup=user_management_keyboard()
            )
        
        await state.clear()
