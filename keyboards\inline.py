from aiogram.types import Inline<PERSON>eyboardMarkup, InlineKeyboardButton
from typing import List, Tuple, Optional


def main_menu_keyboard() -> InlineKeyboardMarkup:
    """Create main menu inline keyboard."""
    buttons = [
        [InlineKeyboardButton(text="🚗 Order Taxi", callback_data="order_taxi")],
        [InlineKeyboardButton(text="📍 My Location", callback_data="my_location")],
        [
            InlineKeyboardButton(text="🕒 History", callback_data="order_history"),
            InlineKeyboardButton(text="⚙️ Settings", callback_data="settings")
        ],
        [InlineKeyboardButton(text="📞 Support", callback_data="support")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def confirmation_keyboard(confirm_data: str = "confirm", cancel_data: str = "cancel") -> InlineKeyboardMarkup:
    """Create confirmation keyboard."""
    buttons = [
        [
            InlineKeyboardButton(text="✅ Yes", callback_data=confirm_data),
            InlineKeyboardButton(text="❌ No", callback_data=cancel_data)
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def back_keyboard(back_data: str = "back") -> InlineKeyboardMarkup:
    """Create back button keyboard."""
    buttons = [
        [InlineKeyboardButton(text="🔙 Back", callback_data=back_data)]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def pagination_keyboard(
    current_page: int, 
    total_pages: int, 
    data_prefix: str = "page"
) -> InlineKeyboardMarkup:
    """Create pagination keyboard."""
    buttons = []
    
    # Navigation buttons
    nav_buttons = []
    if current_page > 1:
        nav_buttons.append(InlineKeyboardButton(text="◀️", callback_data=f"{data_prefix}_{current_page - 1}"))
    
    nav_buttons.append(InlineKeyboardButton(text=f"{current_page}/{total_pages}", callback_data="current_page"))
    
    if current_page < total_pages:
        nav_buttons.append(InlineKeyboardButton(text="▶️", callback_data=f"{data_prefix}_{current_page + 1}"))
    
    if nav_buttons:
        buttons.append(nav_buttons)
    
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def settings_keyboard() -> InlineKeyboardMarkup:
    """Create settings keyboard."""
    buttons = [
        [InlineKeyboardButton(text="📱 Change Phone", callback_data="change_phone")],
        [InlineKeyboardButton(text="📍 Default Location", callback_data="default_location")],
        [InlineKeyboardButton(text="🔔 Notifications", callback_data="notifications")],
        [InlineKeyboardButton(text="🌐 Language", callback_data="language")],
        [InlineKeyboardButton(text="🔙 Back to Menu", callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def taxi_order_keyboard() -> InlineKeyboardMarkup:
    """Create taxi order keyboard."""
    buttons = [
        [InlineKeyboardButton(text="🚗 Economy", callback_data="taxi_economy")],
        [InlineKeyboardButton(text="🚙 Comfort", callback_data="taxi_comfort")],
        [InlineKeyboardButton(text="🚐 Business", callback_data="taxi_business")],
        [InlineKeyboardButton(text="🔙 Back", callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def order_status_keyboard(order_id: str) -> InlineKeyboardMarkup:
    """Create order status keyboard."""
    buttons = [
        [InlineKeyboardButton(text="📍 Track Order", callback_data=f"track_{order_id}")],
        [InlineKeyboardButton(text="📞 Call Driver", callback_data=f"call_{order_id}")],
        [InlineKeyboardButton(text="❌ Cancel Order", callback_data=f"cancel_{order_id}")],
        [InlineKeyboardButton(text="🔙 Back to Menu", callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def create_dynamic_keyboard(
    buttons_data: List[Tuple[str, str]], 
    row_width: int = 2,
    add_back_button: bool = False
) -> InlineKeyboardMarkup:
    """Create dynamic inline keyboard from button data."""
    keyboard = []
    row = []
    
    for text, callback_data in buttons_data:
        row.append(InlineKeyboardButton(text=text, callback_data=callback_data))
        
        if len(row) >= row_width:
            keyboard.append(row)
            row = []
    
    if row:
        keyboard.append(row)
    
    if add_back_button:
        keyboard.append([InlineKeyboardButton(text="🔙 Back", callback_data="back")])
    
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def url_keyboard(url: str, text: str = "Open Link") -> InlineKeyboardMarkup:
    """Create keyboard with URL button."""
    buttons = [
        [InlineKeyboardButton(text=text, url=url)]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def contact_keyboard(phone_number: str, text: str = "📞 Call") -> InlineKeyboardMarkup:
    """Create keyboard with contact button.""" 
    buttons = [
        [InlineKeyboardButton(text=text, callback_data=f"contact_{phone_number}")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)
