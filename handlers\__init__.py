from aiogram import Dispatcher
from .user import user_base_router, taxi_router, delivery_router, driver_router
from .admin import admin_panel_router, user_management_router, statistics_router
from .fallback import router as fallback_router


def setup_routers(dp: Dispatcher) -> None:
    """Setup all routers in priority order using include_routers."""

    # 1. User routers (highest priority)
    dp.include_routers(
        user_base_router,
        taxi_router,
        delivery_router,
        driver_router
    )

    # 2. Admin routers (medium priority)
    dp.include_routers(
        admin_panel_router,
        user_management_router,
        statistics_router
    )

    # 3. Fallback router (lowest priority - catches unhandled requests)
    dp.include_routers(fallback_router)


__all__ = ["setup_routers"]
