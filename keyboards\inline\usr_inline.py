from aiogram.types import Inline<PERSON>eyboardMarkup, InlineKeyboardButton


def taxi_order_keyboard() -> InlineKeyboardMarkup:
    """Create taxi order keyboard."""
    buttons = [
        [InlineKeyboardButton(text="🚗 Economy", callback_data="taxi_economy")],
        [InlineKeyboardButton(text="🚙 Comfort", callback_data="taxi_comfort")],
        [InlineKeyboardButton(text="🚐 Business", callback_data="taxi_business")],
        [InlineKeyboardButton(text="🔙 Back", callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def order_status_keyboard(order_id: str) -> InlineKeyboardMarkup:
    """Create order status keyboard."""
    buttons = [
        [InlineKeyboardButton(text="📍 Track Order", callback_data=f"track_{order_id}")],
        [InlineKeyboardButton(text="📞 Call Driver", callback_data=f"call_{order_id}")],
        [InlineKeyboardButton(text="❌ Cancel Order", callback_data=f"cancel_{order_id}")],
        [InlineKeyboardButton(text="🔙 Back to Menu", callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def confirmation_keyboard(confirm_data: str = "confirm", cancel_data: str = "cancel") -> InlineKeyboardMarkup:
    """Create confirmation keyboard."""
    buttons = [
        [
            InlineKeyboardButton(text="✅ Yes", callback_data=confirm_data),
            InlineKeyboardButton(text="❌ No", callback_data=cancel_data)
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)
