from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton


def admin_main_keyboard() -> InlineKeyboardMarkup:
    """Create admin main menu keyboard."""
    buttons = [
        [InlineKeyboardButton(text="👥 User Management", callback_data="user_management")],
        [InlineKeyboardButton(text="📊 Statistics", callback_data="statistics")],
        [InlineKeyboardButton(text="📢 Broadcast Message", callback_data="broadcast_message")],
        [InlineKeyboardButton(text="⚙️ System Settings", callback_data="system_settings")],
        [InlineKeyboardButton(text="🔙 Back to Bot", callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def user_management_keyboard() -> InlineKeyboardMarkup:
    """Create user management keyboard."""
    buttons = [
        [InlineKeyboardButton(text="📋 List Users", callback_data="list_users")],
        [InlineKeyboardButton(text="🔍 Search User", callback_data="search_user")],
        [InlineKeyboardButton(text="📊 User Stats", callback_data="user_stats")],
        [InlineKeyboardButton(text="🔙 Back to Admin", callback_data="admin_main")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def user_actions_keyboard(user_id: int) -> InlineKeyboardMarkup:
    """Create user actions keyboard."""
    buttons = [
        [InlineKeyboardButton(text="🚫 Ban User", callback_data=f"ban_user_{user_id}")],
        [InlineKeyboardButton(text="✅ Unban User", callback_data=f"unban_user_{user_id}")],
        [InlineKeyboardButton(text="📱 Contact User", callback_data=f"contact_user_{user_id}")],
        [InlineKeyboardButton(text="🔙 Back", callback_data="user_management")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def statistics_keyboard() -> InlineKeyboardMarkup:
    """Create statistics keyboard."""
    buttons = [
        [InlineKeyboardButton(text="👥 User Stats", callback_data="user_stats")],
        [InlineKeyboardButton(text="🚗 Order Stats", callback_data="order_stats")],
        [InlineKeyboardButton(text="🖥️ System Stats", callback_data="system_stats")],
        [InlineKeyboardButton(text="📁 Export Data", callback_data="export_stats")],
        [InlineKeyboardButton(text="🔙 Back to Admin", callback_data="admin_main")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)
