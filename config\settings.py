import os
from dataclasses import dataclass
from typing import Optional, List
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


@dataclass
class Settings:
    BOT_TOKEN: str
    DATABASE_URL: str
    DEBUG: bool
    ADMIN_IDS: List[int]
    DRIVERS_GROUP_ID: int


def get_settings() -> Settings:
    """Get application settings from environment variables."""
    # Parse admin IDs from environment variable
    admin_ids_str = os.getenv("ADMIN_IDS", "")
    admin_ids = []
    if admin_ids_str:
        try:
            admin_ids = [int(id.strip()) for id in admin_ids_str.split(",") if id.strip()]
        except ValueError:
            print("Warning: Invalid ADMIN_IDS format in environment variables")
            admin_ids = []
    
    return Settings(
        BOT_TOKEN=os.getenv("BOT_TOKEN", ""),
        DATABASE_URL=os.getenv("DATABASE_URL", "postgresql://user:password@localhost/taxibot"),
        DEBUG=os.getenv("DEBUG", "False").lower() == "true",
        ADMIN_IDS=admin_ids,
        DRIVERS_GROUP_ID=int(os.getenv("DRIVERS_GROUP_ID", "-1"))
    )